import React, { useState } from 'react'
import { Toaster } from 'react-hot-toast'
import JsonInput from './components/JsonInput'
import YamlOutput from './components/YamlOutput'
import YamlEditor from './components/YamlEditor'
import NaturalLanguagePreview from './components/NaturalLanguagePreview'
import LicenseTable from './components/LicenseTable'
import { formatLicenseTableData, generateLicenseYaml } from './utils/licenseDataParser'

function App() {
  const [jsonInput, setJsonInput] = useState('')
  const [yamlOutput, setYamlOutput] = useState('')
  const [editedYaml, setEditedYaml] = useState('')
  const [naturalLanguage, setNaturalLanguage] = useState('')
  const [licenseData, setLicenseData] = useState(null)

  const handleLicenseData = (parsedLicenses) => {
    if (parsedLicenses && parsedLicenses.length > 0) {
      const formattedData = formatLicenseTableData(parsedLicenses)
      setLicenseData(formattedData)
    } else {
      setLicenseData(null)
    }
  }

  const handleExportLicenseYaml = () => {
    if (licenseData && licenseData.rows) {
      // Convert table data back to license format for YAML export
      const licensesForYaml = licenseData.rows.map(row => ({
        name: row.name,
        skuPartNumber: row.skuId || 'unknown',
        availableLicenses: row.available,
        assignedLicenses: row.assigned,
        usagePercentage: row.usagePercentage,
        accountType: row.accountType
      }))
      const yamlString = generateLicenseYaml(licensesForYaml)
      setYamlOutput(yamlString)
    }
  }

  return (
    <div className="app">
      <Toaster position="top-right" />

      <header className="app-header">
        <h1>JSON to YAML Converter</h1>
        <p>Convert API JSON responses to YAML and manipulate for AI interpretation</p>
      </header>

      <main className="app-main">
        <div className="converter-section">
          <div className="input-section">
            <JsonInput
              value={jsonInput}
              onChange={setJsonInput}
              onConvert={setYamlOutput}
              onLicenseData={handleLicenseData}
            />
          </div>

          <div className="output-section">
            <YamlOutput
              yaml={yamlOutput}
              onEdit={setEditedYaml}
            />
          </div>
        </div>

        {/* License Table Section - only show when license data is available */}
        {licenseData && (
          <div className="license-section">
            <LicenseTable
              licenseData={licenseData}
              onExportYaml={handleExportLicenseYaml}
            />
          </div>
        )}

        <div className="editor-section">
          <div className="yaml-editor">
            <YamlEditor
              value={editedYaml}
              onChange={setEditedYaml}
              onGenerateNaturalLanguage={setNaturalLanguage}
            />
          </div>

          <div className="preview-section">
            <NaturalLanguagePreview
              content={naturalLanguage}
            />
          </div>
        </div>
      </main>
    </div>
  )
}

export default App
