import yaml from 'js-yaml'

/**
 * Converts JSON data to Moveworks Compound Action YAML format
 * Following the specifications from MW_CA_DOC.txt
 */

/**
 * Detects if JSON structure looks like it could be converted to a Moveworks Compound Action
 */
export function isMoveworksCompatible(jsonObject) {
  if (!jsonObject || typeof jsonObject !== 'object') return false

  // Check for common Moveworks patterns
  const hasSteps = Array.isArray(jsonObject.steps)
  const hasActions = Object.values(jsonObject).some(val =>
    val && typeof val === 'object' && (val.action_name || val.code)
  )
  const hasWorkflowStructure = jsonObject.workflow || jsonObject.actions || jsonObject.compound_action

  return hasSteps || hasActions || hasWorkflowStructure
}

/**
 * Detects if JSON structure contains Microsoft 365 license data
 */
export function isLicenseData(jsonObject) {
  if (!jsonObject || typeof jsonObject !== 'object') return false

  // Check for license-specific patterns
  let licenseArray = []

  // Handle different structures
  if (Array.isArray(jsonObject.value)) {
    licenseArray = jsonObject.value
  } else if (Array.isArray(jsonObject.licenses)) {
    licenseArray = jsonObject.licenses
  } else if (jsonObject.microsoft_365_licenses && Array.isArray(jsonObject.microsoft_365_licenses.licenses)) {
    licenseArray = jsonObject.microsoft_365_licenses.licenses
  } else if (Array.isArray(jsonObject)) {
    licenseArray = jsonObject
  }

  if (licenseArray.length > 0) {
    const firstItem = licenseArray[0]
    // Check for Microsoft 365 license properties (both original API format and processed format)
    const hasLicenseProps = firstItem && (
      firstItem.skuPartNumber ||
      firstItem.sku_part_number ||
      firstItem.servicePlans ||
      firstItem.prepaidUnits ||
      firstItem.consumedUnits ||
      firstItem.available_licenses ||
      firstItem.assigned_licenses ||
      firstItem.usage_percentage ||
      (firstItem.displayName && firstItem.displayName.includes('Microsoft')) ||
      (firstItem.name && (firstItem.name.includes('Microsoft') || firstItem.name.includes('ENTERPRISE') || firstItem.name.includes('POWER')))
    )
    return hasLicenseProps
  }

  return false
}

/**
 * Converts a generic JSON object to Moveworks Compound Action format
 */
export function convertToMoveworksYaml(jsonObject, options = {}) {
  const {
    addComments = true,
    useStepsWrapper = true,
    addProgressUpdates = true
  } = options

  let moveworksStructure = {}

  // If already has steps structure, validate and preserve it
  if (jsonObject.steps && Array.isArray(jsonObject.steps)) {
    moveworksStructure = {
      steps: jsonObject.steps.map(step => normalizeStep(step))
    }
  } else {
    // Convert generic structure to Moveworks format
    moveworksStructure = convertGenericToMoveworks(jsonObject, { addProgressUpdates })
  }

  // Apply Moveworks-specific formatting
  const yamlOptions = {
    indent: 2,
    lineWidth: -1,
    noRefs: true,
    sortKeys: false,
    quotingType: '"',
    forceQuotes: false
  }

  let yamlString = yaml.dump(moveworksStructure, yamlOptions)

  if (addComments) {
    yamlString = addMoveworksComments(yamlString, moveworksStructure)
  }

  return yamlString
}

/**
 * Converts control flow structures (switch, for, parallel, etc.)
 */
function convertControlFlowStep(obj) {
  // Handle switch statements
  if (obj.switch) {
    const switchObj = {
      cases: obj.switch.cases ? obj.switch.cases.map(caseItem => ({
        condition: caseItem.condition,
        steps: caseItem.steps ? caseItem.steps.map(step => {
          // Always normalize nested steps to ensure proper structure
          return normalizeStep(step)
        }) : []
      })) : [],
      ...(obj.switch.default ? {
        default: {
          steps: obj.switch.default.steps ? obj.switch.default.steps.map(step => {
            // Always normalize nested steps to ensure proper structure
            return normalizeStep(step)
          }) : []
        }
      } : {})
    }
    return { switch: switchObj }
  }

  // Handle for loops
  if (obj.for) {
    return {
      for: {
        each: obj.for.each || 'item',
        index: obj.for.index || 'index',
        in: obj.for.in || 'data.items',
        output_key: obj.for.output_key || 'loop_results',
        ...(obj.for.steps ? {
          steps: obj.for.steps.map(step => {
            // Always normalize nested steps to ensure proper structure
            return normalizeStep(step)
          })
        } : {})
      }
    }
  }

  // Handle parallel execution
  if (obj.parallel) {
    return {
      parallel: {
        ...(obj.parallel.branches ? { branches: obj.parallel.branches.map(branch => normalizeStep(branch)) } : {}),
        ...(obj.parallel.for ? { for: obj.parallel.for } : {})
      }
    }
  }

  // Handle try_catch
  if (obj.try_catch) {
    return {
      try_catch: {
        try: {
          steps: obj.try_catch.try.steps ? obj.try_catch.try.steps.map(step => normalizeStep(step)) : []
        },
        catch: {
          steps: obj.try_catch.catch.steps ? obj.try_catch.catch.steps.map(step => normalizeStep(step)) : [],
          ...(obj.try_catch.catch.on_status_code ? { on_status_code: obj.try_catch.catch.on_status_code } : {})
        }
      }
    }
  }

  // Handle return statements
  if (obj.return) {
    // Ensure return value is properly structured
    let returnValue = obj.return

    // If return is a primitive value, wrap it in output_mapper
    if (typeof returnValue === 'string' || typeof returnValue === 'number' || typeof returnValue === 'boolean') {
      returnValue = {
        output_mapper: {
          result: returnValue
        }
      }
    } else if (returnValue && typeof returnValue === 'object') {
      // If return is an object but doesn't have output_mapper, wrap it
      if (!returnValue.output_mapper) {
        returnValue = {
          output_mapper: returnValue
        }
      }
    } else {
      // Fallback for null/undefined values
      returnValue = {
        output_mapper: {}
      }
    }

    return {
      return: returnValue
    }
  }

  // Handle raise statements
  if (obj.raise) {
    return {
      raise: {
        output_key: obj.raise.output_key || 'error_result',
        message: obj.raise.message || 'An error occurred'
      }
    }
  }

  return null
}

/**
 * Normalizes a step to ensure proper Moveworks format
 */
export function normalizeStep(step) {
  // Handle null/undefined steps
  if (!step || typeof step !== 'object') {
    return step
  }

  // If step has action_name at top level, wrap it properly
  if (step.action_name) {
    return {
      action: {
        action_name: step.action_name,
        output_key: step.output_key,
        input_args: step.input_args || {},
        ...(step.progress_updates ? { progress_updates: step.progress_updates } : {})
      }
    }
  }

  // If step has code at top level, wrap it properly
  if (step.code) {
    return {
      script: {
        output_key: step.output_key,
        input_args: step.input_args || {},
        code: step.code
      }
    }
  }

  // If step has return at top level, ensure proper structure
  if (step.return !== undefined) {
    let returnValue = step.return

    // Ensure return value is properly structured as an object
    if (typeof returnValue === 'string' || typeof returnValue === 'number' || typeof returnValue === 'boolean') {
      returnValue = {
        output_mapper: {
          result: returnValue
        }
      }
    } else if (returnValue && typeof returnValue === 'object') {
      // If return is an object but doesn't have output_mapper, wrap it
      if (!returnValue.output_mapper) {
        returnValue = {
          output_mapper: returnValue
        }
      }
    } else {
      // Fallback for null/undefined values
      returnValue = {
        output_mapper: {}
      }
    }

    return {
      return: returnValue
    }
  }

  // For control flow structures, process them through convertControlFlowStep
  if (step.switch || step.for || step.parallel || step.try_catch || step.raise) {
    return convertControlFlowStep(step)
  }

  // If already properly structured (has action, script, return, etc.), return as-is
  if (step.action || step.script) {
    return step
  }

  // If none of the above, return as-is
  return step
}

/**
 * Detects if an object contains arrays that should be processed with for loops
 */
function detectArraysForLooping(jsonObject) {
  const arrayFields = []

  if (!jsonObject || typeof jsonObject !== 'object') {
    return arrayFields
  }

  // Helper function to recursively find arrays
  function findArraysRecursively(obj, prefix = '', depth = 0) {
    // Limit recursion depth to avoid infinite loops and excessive complexity
    if (depth > 3) return

    Object.entries(obj).forEach(([key, value]) => {
      const fullKey = prefix ? `${prefix}.${key}` : key

      if (Array.isArray(value) && value.length > 0) {
        // Check if array contains objects (not just primitives)
        const hasObjects = value.some(item => item && typeof item === 'object')
        if (hasObjects) {
          arrayFields.push({
            key: fullKey,
            array: value,
            itemType: inferItemType(key, value),
            variableName: generateLoopVariableName(key),
            originalKey: key,
            depth: depth
          })
        }
      } else if (value && typeof value === 'object' && !Array.isArray(value)) {
        // Recursively check nested objects (go deeper for complex API structures)
        findArraysRecursively(value, fullKey, depth + 1)
      }
    })
  }

  findArraysRecursively(jsonObject)
  return arrayFields
}

/**
 * Infers the type of items in an array for better variable naming
 */
function inferItemType(arrayKey, array) {
  // Common patterns for array naming (expanded for API responses)
  const patterns = {
    // General
    products: 'product',
    items: 'item',
    users: 'user',
    orders: 'order',
    records: 'record',
    entries: 'entry',
    tasks: 'task',
    employees: 'employee',
    customers: 'customer',
    transactions: 'transaction',
    requests: 'request',
    tickets: 'ticket',
    assets: 'asset',
    devices: 'device',
    files: 'file',
    documents: 'document',
    messages: 'message',
    notifications: 'notification',

    // API-specific patterns
    value: 'item',           // Common in Microsoft APIs
    data: 'item',            // Common in GraphQL APIs
    results: 'result',       // Common in search APIs
    issues: 'issue',         // Jira, GitHub
    channels: 'channel',     // Slack, Teams
    repositories: 'repository', // GitHub
    repos: 'repo',           // GitHub short form
    nodes: 'node',           // GraphQL
    edges: 'edge',           // GraphQL
    incidents: 'incident',   // ServiceNow
    changes: 'change',       // ServiceNow
    departments: 'department',
    teams: 'team',
    members: 'member',
    licenses: 'license',     // Microsoft 365
    subscriptions: 'subscription',
    groups: 'group',
    roles: 'role',
    permissions: 'permission',
    policies: 'policy',
    workflows: 'workflow',
    approvals: 'approval',
    comments: 'comment',
    attachments: 'attachment',
    logs: 'log',
    events: 'event',
    alerts: 'alert',
    metrics: 'metric',
    reports: 'report'
  }

  // Check for exact matches first
  const lowerKey = arrayKey.toLowerCase()
  if (patterns[lowerKey]) {
    return patterns[lowerKey]
  }

  // Check for partial matches
  for (const [plural, singular] of Object.entries(patterns)) {
    if (lowerKey.includes(plural)) {
      return singular
    }
  }

  // Try to singularize common endings
  if (lowerKey.endsWith('ies')) {
    return lowerKey.slice(0, -3) + 'y'  // companies -> company
  } else if (lowerKey.endsWith('es') && lowerKey.length > 3) {
    return lowerKey.slice(0, -2)  // boxes -> box
  } else if (lowerKey.endsWith('s') && lowerKey.length > 1) {
    return lowerKey.slice(0, -1)  // items -> item
  }

  // Default fallback
  return 'item'
}

/**
 * Generates appropriate variable names for loop iteration
 */
function generateLoopVariableName(arrayKey) {
  const itemType = inferItemType(arrayKey, [])
  return {
    each: itemType,
    index: `${itemType}_index`,
    outputKey: `${arrayKey}_processing_results`
  }
}

/**
 * Creates a for loop step for processing an array
 */
function createForLoopStep(arrayInfo, addProgressUpdates = true) {
  const { key, array, variableName } = arrayInfo
  const sampleItem = array[0] // Use first item to infer structure

  // Generate steps for processing each item
  const loopSteps = generateItemProcessingSteps(sampleItem, variableName.each, addProgressUpdates)

  // Handle nested paths by converting dots to data access
  const dataPath = key.includes('.') ? `data.${key.replace('.', '.')}` : `data.${key}`

  const forLoopStep = {
    for: {
      each: variableName.each,
      index: variableName.index,
      in: dataPath,
      output_key: variableName.outputKey,
      steps: loopSteps
    }
  }

  return forLoopStep
}

/**
 * Generates steps for processing individual items within a loop
 */
function generateItemProcessingSteps(sampleItem, itemVariable, addProgressUpdates = true) {
  const steps = []

  if (!sampleItem || typeof sampleItem !== 'object') {
    // For primitive items, create a simple processing step
    steps.push({
      script: {
        output_key: 'processed_item',
        input_args: {
          current_item: itemVariable
        },
        code: `# Process individual item\nresult = {\n  "processed_value": current_item,\n  "timestamp": "processed"\n}\nresult`
      }
    })
    return steps
  }

  // Analyze the structure of the sample item
  const itemProperties = Object.keys(sampleItem)

  // Create appropriate processing based on item structure
  if (itemProperties.includes('sku_part_number') || itemProperties.includes('usage_percentage') ||
      itemProperties.includes('available_licenses') || itemProperties.includes('assigned_licenses')) {
    // License-related item
    steps.push(createLicenseProcessingAction(sampleItem, itemVariable, addProgressUpdates))
  } else if (itemProperties.includes('userPrincipalName') || itemProperties.includes('displayName') ||
             (itemProperties.includes('email') || itemProperties.includes('emailAddress'))) {
    // User-related item (Azure AD, general user objects)
    steps.push(createUserProcessingAction(sampleItem, itemVariable, addProgressUpdates))
  } else if (itemProperties.includes('key') && itemProperties.includes('fields')) {
    // Jira issue
    steps.push(createJiraIssueProcessingAction(sampleItem, itemVariable, addProgressUpdates))
  } else if (itemProperties.includes('sys_id') && itemProperties.includes('number')) {
    // ServiceNow incident/record
    steps.push(createServiceNowProcessingAction(sampleItem, itemVariable, addProgressUpdates))
  } else if (itemProperties.includes('name') && (itemProperties.includes('is_channel') || itemProperties.includes('members'))) {
    // Slack channel
    steps.push(createSlackChannelProcessingAction(sampleItem, itemVariable, addProgressUpdates))
  } else if (itemProperties.includes('url') && itemProperties.includes('name') &&
             (itemProperties.includes('issues') || itemProperties.includes('pullRequests'))) {
    // GitHub repository
    steps.push(createGitHubRepoProcessingAction(sampleItem, itemVariable, addProgressUpdates))
  } else if (itemProperties.includes('id') || itemProperties.includes('_id') || itemProperties.includes('sys_id')) {
    // Item has an ID - likely needs individual processing
    steps.push(createItemProcessingAction(sampleItem, itemVariable, addProgressUpdates))
  } else if (itemProperties.includes('url') || itemProperties.includes('endpoint')) {
    // API-related item
    steps.push(createApiProcessingAction(sampleItem, itemVariable, addProgressUpdates))
  } else {
    // Generic item processing
    steps.push(createGenericItemProcessingAction(sampleItem, itemVariable, addProgressUpdates))
  }

  return steps
}

/**
 * Converts generic JSON structure to Moveworks Compound Action format
 */
function convertGenericToMoveworks(jsonObject, options = {}) {
  const { addProgressUpdates } = options

  // If it's a simple object, create a basic compound action
  if (!Array.isArray(jsonObject) && typeof jsonObject === 'object') {
    const steps = []

    // First, check for arrays that should be processed with for loops
    const arrayFields = detectArraysForLooping(jsonObject)

    if (arrayFields.length > 0) {
      // Add initial data preparation step if needed
      const nonArrayData = Object.fromEntries(
        Object.entries(jsonObject).filter(([key, value]) =>
          !arrayFields.some(field => field.key === key || field.key.startsWith(`${key}.`))
        )
      )

      if (Object.keys(nonArrayData).length > 0) {
        steps.push({
          script: {
            output_key: 'initial_data_setup',
            input_args: nonArrayData,
            code: `# Set up initial data for processing\nresult = {\n${Object.keys(nonArrayData).map(key => `  "${key}": ${key}`).join(',\n')}\n}\nresult`
          }
        })
      }

      // Create for loop steps for each array
      arrayFields.forEach(arrayInfo => {
        const forLoopStep = createForLoopStep(arrayInfo, addProgressUpdates)
        steps.push(forLoopStep)
      })

      // Add a final aggregation step if multiple arrays were processed
      if (arrayFields.length > 1) {
        steps.push({
          script: {
            output_key: 'final_aggregated_results',
            input_args: Object.fromEntries(
              arrayFields.map(field => [field.variableName.outputKey, `data.${field.variableName.outputKey}`])
            ),
            code: `# Aggregate results from all processed arrays\nresult = {\n${arrayFields.map(field => `  "${field.originalKey || field.key}_results": ${field.variableName.outputKey}`).join(',\n')}\n}\nresult`
          }
        })
      }
    } else {
      // No arrays detected - use original logic
      // Check for workflow-like patterns first
      if (jsonObject.workflow || jsonObject.actions || jsonObject.tasks) {
        const workflowSteps = jsonObject.workflow || jsonObject.actions || jsonObject.tasks
        if (Array.isArray(workflowSteps)) {
          workflowSteps.forEach((step, index) => {
            const convertedStep = convertObjectToAction(`step_${index + 1}`, step, index, addProgressUpdates)
            if (convertedStep) steps.push(convertedStep)
          })
        }
      } else {
        // Look for action-like structures
        Object.entries(jsonObject).forEach(([key, value], index) => {
          if (value && typeof value === 'object' && !Array.isArray(value)) {
            const step = convertObjectToAction(key, value, index, addProgressUpdates)
            if (step) steps.push(step)
          } else if (typeof value === 'string' || typeof value === 'number') {
            // Create a simple return step for primitive values
            steps.push({
              return: {
                output_mapper: {
                  [key]: typeof value === 'string' ? `"${value}"` : value
                }
              }
            })
          }
        })
      }
    }

    // If no steps were created, create a basic script action
    if (steps.length === 0) {
      steps.push(createBasicScriptAction(jsonObject))
    }

    return { steps }
  }

  // If it's an array at the root level, create a for loop to process it
  if (Array.isArray(jsonObject)) {
    const steps = []

    if (jsonObject.length > 0 && jsonObject.some(item => item && typeof item === 'object')) {
      // Create a for loop for the root array
      const arrayInfo = {
        key: 'input_array',
        array: jsonObject,
        itemType: 'item',
        variableName: {
          each: 'item',
          index: 'item_index',
          outputKey: 'array_processing_results'
        }
      }

      // First, store the input array in data
      steps.push({
        script: {
          output_key: 'input_array',
          code: `# Store input array for processing\nresult = ${JSON.stringify(jsonObject)}\nresult`
        }
      })

      // Then create the for loop
      const forLoopStep = createForLoopStep(arrayInfo, addProgressUpdates)
      steps.push(forLoopStep)
    } else {
      // Fallback to original logic for simple arrays
      const processedSteps = jsonObject.map((item, index) => {
        if (typeof item === 'object' && item !== null) {
          return convertObjectToAction(`step_${index + 1}`, item, index, addProgressUpdates)
        }
        return createBasicScriptAction({ value: item }, `step_${index + 1}`)
      }).filter(Boolean)

      steps.push(...processedSteps)
    }

    return { steps }
  }

  // For primitive values, create a simple return action
  return {
    steps: [{
      return: {
        output_mapper: {
          result: typeof jsonObject === 'string' ? `"${jsonObject}"` : jsonObject
        }
      }
    }]
  }
}

/**
 * Converts an object to a Moveworks action step
 */
function convertObjectToAction(key, obj, index, addProgressUpdates = true) {
  // Check for Moveworks control flow structures first
  if (obj.switch || obj.for || obj.parallel || obj.try_catch || obj.return || obj.raise) {
    return convertControlFlowStep(obj)
  }

  // Check if it already looks like a Moveworks action
  if (obj.action_name) {
    // Ensure proper action structure
    return {
      action: {
        action_name: obj.action_name,
        output_key: obj.output_key || `${key}_result`,
        input_args: obj.input_args || {},
        ...(addProgressUpdates && obj.progress_updates ? { progress_updates: obj.progress_updates } : {})
      }
    }
  }

  // Check if it's a script
  if (obj.code) {
    return {
      script: {
        output_key: obj.output_key || `${key}_result`,
        input_args: obj.input_args || {},
        code: obj.code
      }
    }
  }

  // Check for HTTP-like structure
  if (obj.url || obj.endpoint || obj.method) {
    return createHttpAction(key, obj, addProgressUpdates)
  }

  // Check for user-related data (might use mw.get_user_by_email)
  if (obj.email || obj.user_email || obj.user_id) {
    return createUserAction(key, obj, addProgressUpdates)
  }

  // Check for notification-like structure
  if (obj.message || obj.notification || obj.alert) {
    return createNotificationAction(key, obj, addProgressUpdates)
  }

  // Default: create a script action to process the data
  return createScriptAction(key, obj, index)
}



/**
 * Creates an HTTP action step
 */
function createHttpAction(key, obj, addProgressUpdates) {
  const action = {
    action_name: obj.action_name || `${key}_http_call`,
    output_key: `${key}_result`,
    input_args: {}
  }

  // Map common HTTP fields with proper data access patterns
  if (obj.url) action.input_args.url = obj.url.startsWith('data.') ? obj.url : `data.${key}_url`
  if (obj.method) action.input_args.method = obj.method
  if (obj.headers) action.input_args.headers = obj.headers
  if (obj.body || obj.data) action.input_args.body = obj.body || obj.data

  // Add any additional input arguments from the object
  Object.entries(obj).forEach(([prop, value]) => {
    if (!['url', 'method', 'headers', 'body', 'data', 'action_name'].includes(prop)) {
      action.input_args[prop] = typeof value === 'string' && !value.startsWith('data.') && !value.startsWith('"')
        ? `data.${prop}`
        : value
    }
  })

  if (addProgressUpdates) {
    action.progress_updates = {
      on_pending: `"Making HTTP request..."`,
      on_complete: `"HTTP request completed successfully."`
    }
  }

  return { action }
}

/**
 * Creates a user lookup action using Moveworks built-in
 */
function createUserAction(key, obj, addProgressUpdates) {
  const action = {
    action_name: 'mw.get_user_by_email',
    output_key: `${key}_user_details`,
    input_args: {}
  }

  // Handle different user identifier patterns
  if (obj.email) {
    action.input_args.user_email = obj.email.startsWith('data.') ? obj.email : `data.${key}_email`
  } else if (obj.user_email) {
    action.input_args.user_email = obj.user_email.startsWith('data.') ? obj.user_email : obj.user_email
  } else if (obj.user_id) {
    // If we have user_id, we might want to use a different action or convert to email lookup
    action.input_args.user_email = obj.user_id.startsWith('data.') ? obj.user_id : `data.${key}_user_id`
  } else {
    // Default fallback
    action.input_args.user_email = `data.user_email`
  }

  if (addProgressUpdates) {
    action.progress_updates = {
      on_pending: `"Looking up user details..."`,
      on_complete: `"User details retrieved successfully."`
    }
  }

  return { action }
}

/**
 * Creates a notification action using Moveworks built-in
 */
function createNotificationAction(key, obj, addProgressUpdates) {
  const action = {
    action_name: 'mw.send_plaintext_chat_notification',
    output_key: `${key}_notification_status`,
    input_args: {}
  }

  // Handle user record ID with proper data access
  if (obj.user_id) {
    action.input_args.user_record_id = obj.user_id.startsWith('data.') ? obj.user_id : `data.${key}_user_id`
  } else if (obj.recipient_id) {
    action.input_args.user_record_id = obj.recipient_id.startsWith('data.') ? obj.recipient_id : `data.${key}_recipient_id`
  } else {
    action.input_args.user_record_id = `data.user_details.user.id`
  }

  // Handle message content
  if (obj.message) {
    action.input_args.message = obj.message
  } else if (obj.notification) {
    action.input_args.message = obj.notification
  } else if (obj.alert) {
    action.input_args.message = obj.alert
  } else {
    action.input_args.message = `"Notification from ${key}"`
  }

  if (addProgressUpdates) {
    action.progress_updates = {
      on_pending: `"Sending notification..."`,
      on_complete: `"Notification sent successfully."`
    }
  }

  return { action }
}

/**
 * Creates a script action for data processing
 */
function createScriptAction(key, obj, index) {
  const inputArgs = {}

  // Convert object properties to input arguments with proper data access
  Object.entries(obj).forEach(([prop, value]) => {
    if (typeof value !== 'object') {
      // Use proper data access patterns
      if (typeof value === 'string' && value.startsWith('data.')) {
        inputArgs[prop] = value
      } else {
        inputArgs[prop] = `data.${prop}`
      }
    }
  })

  const code = generateAPIthonCode(obj, key)

  return {
    script: {
      output_key: `${key}_processed`,
      input_args: inputArgs,
      code: code
    }
  }
}

/**
 * Creates a basic script action for simple data
 */
function createBasicScriptAction(obj, key = 'data_processing') {
  return {
    script: {
      output_key: `${key}_result`,
      input_args: {
        input_data: 'data.input_data'
      },
      code: `# Process the input data\nresult = input_data\nresult`
    }
  }
}

/**
 * Creates an action for processing license items
 */
function createLicenseProcessingAction(sampleItem, itemVariable, addProgressUpdates = true) {
  const properties = Object.keys(sampleItem)

  const action = {
    script: {
      output_key: 'license_analysis_result',
      input_args: {
        license_data: itemVariable
      },
      code: `# Process Microsoft 365 license data
license_name = license_data.get("name", "Unknown License")
available = license_data.get("available_licenses", 0)
assigned = license_data.get("assigned_licenses", 0)
usage_pct = license_data.get("usage_percentage", 0)

# Determine license status and recommendations
status = "normal"
recommendation = "No action needed"

if usage_pct >= 95:
    status = "critical"
    recommendation = "Consider purchasing additional licenses"
elif usage_pct >= 85:
    status = "warning"
    recommendation = "Monitor usage closely"
elif usage_pct <= 10 and available > 100:
    status = "underutilized"
    recommendation = "Consider reducing license count"

processed_license = {
  "license_name": license_name,
  "sku_part_number": license_data.get("sku_part_number", "unknown"),
  "usage_metrics": {
    "available": available,
    "assigned": assigned,
    "usage_percentage": usage_pct,
    "remaining": available - assigned
  },
  "status": status,
  "recommendation": recommendation,
  "account_type": license_data.get("account_type", "Organization"),
  "analysis_timestamp": "processed"
}
processed_license`
    }
  }

  if (addProgressUpdates) {
    action.script.progress_updates = {
      on_pending: 'Analyzing license usage and generating recommendations...',
      on_complete: 'License analysis completed'
    }
  }

  return action
}

/**
 * Creates an action for processing items with IDs
 */
function createItemProcessingAction(sampleItem, itemVariable, addProgressUpdates = true) {
  const properties = Object.keys(sampleItem)
  const hasId = properties.includes('id') || properties.includes('_id')
  const idField = properties.includes('id') ? 'id' : '_id'

  const action = {
    script: {
      output_key: 'processed_item_data',
      input_args: {
        item_data: itemVariable
      },
      code: `# Process item with ID
item_id = item_data.get("${idField}", "unknown")
processed_result = {
  "item_id": item_id,
  "processing_status": "completed",
  "processed_fields": {
${properties.filter(p => p !== idField).map(prop => `    "${prop}": item_data.get("${prop}", None)`).join(',\n')}
  },
  "timestamp": "processed"
}
processed_result`
    }
  }

  if (addProgressUpdates) {
    action.script.progress_updates = {
      on_pending: `Processing item with ${idField}...`,
      on_complete: `Item processing completed`
    }
  }

  return action
}

/**
 * Creates an action for processing user-related items
 */
function createUserProcessingAction(sampleItem, itemVariable, addProgressUpdates = true) {
  const properties = Object.keys(sampleItem)
  const emailField = properties.find(p => p.includes('email')) || 'email'

  const action = {
    action: {
      action_name: 'mw.get_user_by_email',
      output_key: 'user_lookup_result',
      input_args: {
        user_email: `${itemVariable}.${emailField}`
      }
    }
  }

  if (addProgressUpdates) {
    action.action.progress_updates = {
      on_pending: 'Looking up user information...',
      on_complete: 'User lookup completed'
    }
  }

  return action
}

/**
 * Creates an action for processing API-related items
 */
function createApiProcessingAction(sampleItem, itemVariable, addProgressUpdates = true) {
  const properties = Object.keys(sampleItem)
  const urlField = properties.find(p => p.includes('url') || p.includes('endpoint')) || 'url'

  const action = {
    script: {
      output_key: 'api_processing_result',
      input_args: {
        api_data: itemVariable
      },
      code: `# Process API-related item
api_url = api_data.get("${urlField}", "")
result = {
  "api_endpoint": api_url,
  "processing_status": "prepared",
  "request_data": {
${properties.filter(p => p !== urlField).map(prop => `    "${prop}": api_data.get("${prop}", None)`).join(',\n')}
  },
  "ready_for_call": True if api_url else False
}
result`
    }
  }

  if (addProgressUpdates) {
    action.script.progress_updates = {
      on_pending: 'Preparing API request...',
      on_complete: 'API request prepared'
    }
  }

  return action
}

/**
 * Creates an action for processing Jira issues
 */
function createJiraIssueProcessingAction(sampleItem, itemVariable, addProgressUpdates = true) {
  const action = {
    script: {
      output_key: 'jira_issue_analysis',
      input_args: {
        issue_data: itemVariable
      },
      code: `# Process Jira issue
issue_key = issue_data.get("key", "unknown")
issue_id = issue_data.get("id", "unknown")
fields = issue_data.get("fields", {})

# Extract key information
summary = fields.get("summary", "No summary")
status = fields.get("status", {}).get("name", "Unknown")
assignee_email = fields.get("assignee", {}).get("emailAddress", None)

# Determine priority and urgency
priority = "medium"  # Default
if "urgent" in summary.lower() or "critical" in summary.lower():
    priority = "high"
elif "minor" in summary.lower() or "trivial" in summary.lower():
    priority = "low"

processed_issue = {
  "issue_key": issue_key,
  "issue_id": issue_id,
  "summary": summary,
  "status": status,
  "assignee_email": assignee_email,
  "priority": priority,
  "needs_attention": status in ["To Do", "Open", "Reopened"],
  "processing_timestamp": "completed"
}
processed_issue`
    }
  }

  if (addProgressUpdates) {
    action.script.progress_updates = {
      on_pending: 'Analyzing Jira issue...',
      on_complete: 'Jira issue analysis completed'
    }
  }

  return action
}

/**
 * Creates an action for processing ServiceNow records
 */
function createServiceNowProcessingAction(sampleItem, itemVariable, addProgressUpdates = true) {
  const action = {
    script: {
      output_key: 'servicenow_record_analysis',
      input_args: {
        record_data: itemVariable
      },
      code: `# Process ServiceNow record
sys_id = record_data.get("sys_id", "unknown")
number = record_data.get("number", "unknown")
description = record_data.get("short_description", "No description")
state = record_data.get("state", "unknown")

# Map state codes to readable status
state_mapping = {
  "1": "New",
  "2": "In Progress",
  "3": "On Hold",
  "6": "Resolved",
  "7": "Closed"
}
status = state_mapping.get(str(state), f"State {state}")

# Determine urgency
urgency = "medium"
if "urgent" in description.lower() or "critical" in description.lower():
    urgency = "high"
elif "low" in description.lower() or "minor" in description.lower():
    urgency = "low"

processed_record = {
  "sys_id": sys_id,
  "number": number,
  "description": description,
  "status": status,
  "urgency": urgency,
  "needs_action": status in ["New", "In Progress"],
  "processing_timestamp": "completed"
}
processed_record`
    }
  }

  if (addProgressUpdates) {
    action.script.progress_updates = {
      on_pending: 'Processing ServiceNow record...',
      on_complete: 'ServiceNow record processed'
    }
  }

  return action
}

/**
 * Creates an action for processing Slack channels
 */
function createSlackChannelProcessingAction(sampleItem, itemVariable, addProgressUpdates = true) {
  const action = {
    script: {
      output_key: 'slack_channel_analysis',
      input_args: {
        channel_data: itemVariable
      },
      code: `# Process Slack channel
channel_id = channel_data.get("id", "unknown")
channel_name = channel_data.get("name", "unknown")
is_private = channel_data.get("is_private", False)
members = channel_data.get("members", [])
member_count = len(members) if isinstance(members, list) else 0

# Determine channel type and activity level
channel_type = "private" if is_private else "public"
activity_level = "high" if member_count > 50 else "medium" if member_count > 10 else "low"

processed_channel = {
  "channel_id": channel_id,
  "channel_name": channel_name,
  "channel_type": channel_type,
  "member_count": member_count,
  "activity_level": activity_level,
  "is_large_channel": member_count > 100,
  "processing_timestamp": "completed"
}
processed_channel`
    }
  }

  if (addProgressUpdates) {
    action.script.progress_updates = {
      on_pending: 'Analyzing Slack channel...',
      on_complete: 'Slack channel analysis completed'
    }
  }

  return action
}

/**
 * Creates an action for processing GitHub repositories
 */
function createGitHubRepoProcessingAction(sampleItem, itemVariable, addProgressUpdates = true) {
  const action = {
    script: {
      output_key: 'github_repo_analysis',
      input_args: {
        repo_data: itemVariable
      },
      code: `# Process GitHub repository
repo_id = repo_data.get("id", "unknown")
repo_name = repo_data.get("name", "unknown")
repo_url = repo_data.get("url", "")
issues_count = repo_data.get("issues", {}).get("totalCount", 0)
pr_count = repo_data.get("pullRequests", {}).get("totalCount", 0)

# Determine activity level and health
total_activity = issues_count + pr_count
if total_activity > 50:
    activity_level = "high"
elif total_activity > 10:
    activity_level = "medium"
else:
    activity_level = "low"

# Health assessment
health_status = "healthy"
if issues_count > pr_count * 3:  # Too many issues vs PRs
    health_status = "needs_attention"
elif issues_count == 0 and pr_count == 0:
    health_status = "inactive"

processed_repo = {
  "repo_id": repo_id,
  "repo_name": repo_name,
  "repo_url": repo_url,
  "issues_count": issues_count,
  "pr_count": pr_count,
  "activity_level": activity_level,
  "health_status": health_status,
  "total_activity": total_activity,
  "processing_timestamp": "completed"
}
processed_repo`
    }
  }

  if (addProgressUpdates) {
    action.script.progress_updates = {
      on_pending: 'Analyzing GitHub repository...',
      on_complete: 'GitHub repository analysis completed'
    }
  }

  return action
}

/**
 * Creates a generic action for processing any item
 */
function createGenericItemProcessingAction(sampleItem, itemVariable, addProgressUpdates = true) {
  const properties = Object.keys(sampleItem)

  const action = {
    script: {
      output_key: 'generic_item_result',
      input_args: {
        current_item: itemVariable
      },
      code: `# Process generic item
# Extract and validate all available fields
processed_item = {
  "original_data": current_item,
  "extracted_fields": {
${properties.map(prop => `    "${prop}": current_item.get("${prop}", None)`).join(',\n')}
  },
  "field_count": len(current_item.keys()) if hasattr(current_item, 'keys') else 0,
  "processing_timestamp": "completed",
  "item_type": "generic"
}
processed_item`
    }
  }

  if (addProgressUpdates) {
    action.script.progress_updates = {
      on_pending: 'Processing item data...',
      on_complete: 'Item data processed successfully'
    }
  }

  return action
}

/**
 * Generates APIthon code for processing an object
 */
function generateAPIthonCode(obj, key) {
  const properties = Object.keys(obj)

  if (properties.length === 1) {
    const prop = properties[0]
    return `# Process ${key} - ${prop}\nresult = ${prop}\nresult`
  }

  if (properties.length === 0) {
    return `# No data to process for ${key}\nresult = "No data available"\nresult`
  }

  return `# Process ${key} data
# Combine all input properties into a result object
result = {
${properties.map(prop => `  "${prop}": ${prop}`).join(',\n')}
}
result`
}

/**
 * Adds helpful comments to the YAML for Moveworks context
 */
function addMoveworksComments(yamlString, structure) {
  let commented = `# Moveworks Compound Action
# Generated from JSON input - review and customize as needed
#
# Key concepts:
# - Use 'data.' prefix to access input variables and previous step outputs
# - Built-in actions use 'mw.' prefix (e.g., mw.get_user_by_email)
# - Each action needs: action_name, output_key, input_args
# - Scripts use APIthon (Python-like) syntax
# - Control flow: switch, for, parallel, try_catch, return, raise
# - Data mapping: Use RENDER(), CONCAT(), MAP() for complex transformations
#
# For more details, see: https://help.moveworks.com/docs/compound-actions

`

  return commented + yamlString
}

/**
 * Validates if YAML follows Moveworks Compound Action format
 */
export function validateMoveworksYaml(yamlString) {
  try {
    const parsed = yaml.load(yamlString)
    const issues = []

    // Check for steps structure
    if (!parsed.steps && !parsed.action && !parsed.script && !parsed.return) {
      issues.push("Missing 'steps' array or single action/script/return")
    }

    // Validate steps structure
    if (parsed.steps) {
      if (!Array.isArray(parsed.steps)) {
        issues.push(`'steps' must be an array, got ${typeof parsed.steps}`)
        return {
          isValid: false,
          issues: issues
        }
      }

      // Check for malformed step keys
      parsed.steps.forEach((step, index) => {
        if (step && typeof step === 'object') {
          // Check for hyphen-prefixed keys (common YAML parsing error)
          const keys = Object.keys(step)
          const malformedKeys = keys.filter(key => key.startsWith('-'))
          if (malformedKeys.length > 0) {
            issues.push(`Step ${index + 1}: Invalid key format '${malformedKeys.join(', ')}'. Remove leading hyphens.`)
          }

          // Check for valid step types
          const validStepTypes = ['action', 'script', 'switch', 'for', 'parallel', 'try_catch', 'return', 'raise']
          if (keys.some(key => validStepTypes.includes(key))) {
            // This is a valid step structure
          } else if (keys.length === 1 && typeof step[keys[0]] === 'object') {
            // Check if this looks like a malformed step
            const stepContent = step[keys[0]]
            if (stepContent && typeof stepContent === 'object' &&
                (stepContent.action_name || stepContent.output_key || stepContent.input_args)) {
              issues.push(`Step ${index + 1}: Malformed step structure. Expected step type (${validStepTypes.join(', ')}) but found '${keys[0]}'.`)
            }
          }
        }

        validateStep(step, index, issues)
      })
    }

    return {
      isValid: issues.length === 0,
      issues: issues
    }
  } catch (error) {
    return {
      isValid: false,
      issues: [`Invalid YAML syntax: ${error.message}`]
    }
  }
}

/**
 * Validates a single step in the compound action
 */
function validateStep(step, index, issues) {
  // Handle null/undefined steps
  if (!step || typeof step !== 'object') {
    issues.push(`Step ${index + 1}: Step must be an object, got ${typeof step}`)
    return
  }

  const stepTypes = ['action', 'script', 'switch', 'for', 'parallel', 'try_catch', 'return', 'raise']
  const keys = Object.keys(step)

  // Check for hyphen-prefixed keys (already checked in parent, but double-check)
  const malformedKeys = keys.filter(key => key.startsWith('-'))
  if (malformedKeys.length > 0) {
    malformedKeys.forEach(key => {
      const correctedKey = key.substring(1) // Remove leading hyphen
      if (stepTypes.includes(correctedKey)) {
        issues.push(`Step ${index + 1}: Invalid key '${key}' should be '${correctedKey}'`)
      } else {
        issues.push(`Step ${index + 1}: Invalid key format '${key}'. Remove leading hyphen.`)
      }
    })
    return // Don't continue validation if keys are malformed
  }

  const hasValidType = stepTypes.some(type => step[type])

  if (!hasValidType) {
    // Check if this looks like a malformed action step
    if (step.action_name || step.output_key || step.input_args) {
      issues.push(`Step ${index + 1}: Appears to be an action but missing 'action' wrapper. Should be: action: { action_name: '${step.action_name || 'action_name'}', ... }`)
    } else if (step.code) {
      issues.push(`Step ${index + 1}: Appears to be a script but missing 'script' wrapper. Should be: script: { code: '...', ... }`)
    } else {
      issues.push(`Step ${index + 1}: Must contain one of: ${stepTypes.join(', ')}`)
    }
  }

  // Validate action steps
  if (step.action) {
    if (typeof step.action !== 'object' || step.action === null) {
      issues.push(`Step ${index + 1}: Action must be an object, got ${typeof step.action}`)
    } else {
      if (!step.action.action_name) {
        issues.push(`Step ${index + 1}: Action missing 'action_name'`)
      }
      if (!step.action.output_key) {
        issues.push(`Step ${index + 1}: Action missing 'output_key'`)
      }
    }
  }

  // Validate script steps
  if (step.script) {
    if (typeof step.script !== 'object' || step.script === null) {
      issues.push(`Step ${index + 1}: Script must be an object, got ${typeof step.script}`)
    } else {
      if (!step.script.code) {
        issues.push(`Step ${index + 1}: Script missing 'code'`)
      }
      if (!step.script.output_key) {
        issues.push(`Step ${index + 1}: Script missing 'output_key'`)
      }
    }
  }

  // Validate return steps
  if (step.return) {
    if (typeof step.return !== 'object' || step.return === null) {
      issues.push(`Step ${index + 1}: Return must be an object, got ${typeof step.return}`)
    } else if (!step.return.output_mapper) {
      issues.push(`Step ${index + 1}: Return missing 'output_mapper'`)
    }
  }

  // Validate raise steps
  if (step.raise) {
    if (typeof step.raise !== 'object' || step.raise === null) {
      issues.push(`Step ${index + 1}: Raise must be an object, got ${typeof step.raise}`)
    } else if (!step.raise.output_key) {
      issues.push(`Step ${index + 1}: Raise missing 'output_key'`)
    }
  }

  // Validate control flow steps
  if (step.switch) {
    if (typeof step.switch !== 'object' || step.switch === null) {
      issues.push(`Step ${index + 1}: Switch must be an object, got ${typeof step.switch}`)
    } else {
      if (!step.switch.cases || !Array.isArray(step.switch.cases)) {
        issues.push(`Step ${index + 1}: Switch missing 'cases' array`)
      }
    }
  }

  if (step.for) {
    if (typeof step.for !== 'object' || step.for === null) {
      issues.push(`Step ${index + 1}: For loop must be an object, got ${typeof step.for}`)
    } else {
      if (!step.for.each) {
        issues.push(`Step ${index + 1}: For loop missing 'each' parameter`)
      }
      if (!step.for.in) {
        issues.push(`Step ${index + 1}: For loop missing 'in' parameter`)
      }
    }
  }
}

/**
 * Attempts to fix common YAML structure issues
 */
export function fixYamlStructureIssues(yamlString) {
  try {
    let fixedYaml = yamlString
    const fixes = []

    // Fix 1: The main issue - convert "-action:" to "- action:"
    // This handles the specific error you encountered
    if (fixedYaml.includes('-action:')) {
      fixedYaml = fixedYaml.replace(/(\s+)-action:/g, '$1- action:')
      fixes.push('Fixed malformed "-action:" to "- action:"')
    }

    // Fix 2: Convert "-script:" to "- script:"
    if (fixedYaml.includes('-script:')) {
      fixedYaml = fixedYaml.replace(/(\s+)-script:/g, '$1- script:')
      fixes.push('Fixed malformed "-script:" to "- script:"')
    }

    // Fix 3: Convert "-return:" to "- return:"
    if (fixedYaml.includes('-return:')) {
      fixedYaml = fixedYaml.replace(/(\s+)-return:/g, '$1- return:')
      fixes.push('Fixed malformed "-return:" to "- return:"')
    }

    // Fix 4: Convert other common step types
    const stepTypes = ['switch', 'for', 'parallel', 'try_catch', 'raise']
    stepTypes.forEach(stepType => {
      const malformedPattern = `-${stepType}:`
      if (fixedYaml.includes(malformedPattern)) {
        const regex = new RegExp(`(\\s+)-${stepType}:`, 'g')
        fixedYaml = fixedYaml.replace(regex, `$1- ${stepType}:`)
        fixes.push(`Fixed malformed "-${stepType}:" to "- ${stepType}:"`)
      }
    })

    // Fix 5: Convert steps object to array if the first step doesn't start with "-"
    const stepsObjectRegex = /^steps:\s*\n(\s+)([^-\s]\w*):/m
    if (stepsObjectRegex.test(fixedYaml)) {
      fixedYaml = fixedYaml.replace(/^steps:\s*\n(\s+)([^-\s]\w*):/m, 'steps:\n$1- $2:')
      fixes.push('Converted steps object to array format')
    }

    return {
      fixedYaml,
      fixes,
      hasChanges: fixes.length > 0
    }
  } catch (error) {
    return {
      fixedYaml: yamlString,
      fixes: [],
      hasChanges: false,
      error: error.message
    }
  }
}

/**
 * Debug function to help identify return step issues
 */
export function debugReturnSteps(jsonObject) {
  const issues = []

  function checkReturnSteps(obj, path = '') {
    if (!obj || typeof obj !== 'object') return

    if (Array.isArray(obj)) {
      obj.forEach((item, index) => {
        checkReturnSteps(item, `${path}[${index}]`)
      })
    } else {
      Object.entries(obj).forEach(([key, value]) => {
        const currentPath = path ? `${path}.${key}` : key

        if (key === 'return') {
          if (typeof value !== 'object' || value === null) {
            issues.push({
              path: currentPath,
              issue: `Return value is ${typeof value}, expected object`,
              value: value
            })
          } else if (!value.output_mapper && typeof value === 'object') {
            issues.push({
              path: currentPath,
              issue: 'Return object missing output_mapper property',
              value: value
            })
          }
        } else if (key === 'steps' && Array.isArray(value)) {
          value.forEach((step, stepIndex) => {
            checkReturnSteps(step, `${currentPath}[${stepIndex}]`)
          })
        } else {
          checkReturnSteps(value, currentPath)
        }
      })
    }
  }

  checkReturnSteps(jsonObject)
  return issues
}

/**
 * Gets suggestions for improving Moveworks YAML
 */
export function getMoveworksSuggestions(yamlString) {
  const suggestions = []

  try {
    const parsed = yaml.load(yamlString)

    // Check for data access patterns
    const yamlText = yamlString.toLowerCase()
    if (!yamlText.includes('data.')) {
      suggestions.push({
        type: 'improvement',
        message: "Consider using 'data.' prefix to access input variables and previous step outputs"
      })
    }

    // Check for built-in actions
    if (yamlText.includes('get_user') && !yamlText.includes('mw.get_user')) {
      suggestions.push({
        type: 'improvement',
        message: "Use 'mw.get_user_by_email' for user lookups instead of custom actions"
      })
    }

    // Check for progress updates
    if (yamlText.includes('action_name') && !yamlText.includes('progress_updates')) {
      suggestions.push({
        type: 'enhancement',
        message: "Consider adding 'progress_updates' to actions for better user experience"
      })
    }

    // Check for error handling
    if (parsed.steps && parsed.steps.length > 1 && !yamlText.includes('try_catch')) {
      suggestions.push({
        type: 'enhancement',
        message: "Consider adding error handling with 'try_catch' for robust workflows"
      })
    }

  } catch (error) {
    suggestions.push({
      type: 'error',
      message: `Fix YAML syntax errors first: ${error.message}`
    })
  }

  return suggestions
}
