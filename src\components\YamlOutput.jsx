import React from 'react'
import AceEditor from 'react-ace'
import toast from 'react-hot-toast'

import 'ace-builds/src-noconflict/mode-yaml'
import 'ace-builds/src-noconflict/theme-github'

function YamlOutput({ yaml, onEdit }) {
  const handleCopy = () => {
    if (!yaml) {
      toast.error('No YAML to copy')
      return
    }

    navigator.clipboard.writeText(yaml).then(() => {
      toast.success('YAML copied to clipboard!')
    }).catch(() => {
      toast.error('Failed to copy YAML')
    })
  }

  const handleEdit = () => {
    if (!yaml) {
      toast.error('No YAML to edit')
      return
    }

    onEdit(yaml)
    toast.success('YAML loaded into editor!')
  }

  return (
    <div className="yaml-output">
      <div className="section-header">
        <h2>🚀 Moveworks YAML Output</h2>
        <div className="button-group">
          <button onClick={handleCopy} className="btn btn-secondary" disabled={!yaml}>
            📋 Copy YAML
          </button>
          <button onClick={handleEdit} className="btn btn-primary" disabled={!yaml}>
            ✏️ Edit YAML
          </button>
        </div>
      </div>

      <AceEditor
        mode="yaml"
        theme="github"
        value={yaml}
        name="yaml-output-editor"
        editorProps={{ $blockScrolling: true }}
        width="100%"
        height="400px"
        fontSize={14}
        showPrintMargin={false}
        showGutter={true}
        highlightActiveLine={false}
        readOnly={true}
        setOptions={{
          showLineNumbers: true,
          tabSize: 2,
          useWorker: false
        }}
        placeholder="Converted YAML will appear here..."
      />
    </div>
  )
}

export default YamlOutput
