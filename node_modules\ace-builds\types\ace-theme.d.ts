/* This file is generated using `npm run update-types` */

declare module "ace-builds/src-noconflict/theme-textmate-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-textmate" {
    export const isDark: false;
    export const cssClass: "ace-tm";
    export const cssText: string;
    export const $id: "ace/theme/textmate";
}
declare module "ace-builds/src-noconflict/theme-ambiance-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-ambiance" {
    export const isDark: true;
    export const cssClass: "ace-ambiance";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-chaos-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-chaos" {
    export const isDark: true;
    export const cssClass: "ace-chaos";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-chrome-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-chrome" {
    export const isDark: false;
    export const cssClass: "ace-chrome";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-cloud9_day-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-cloud9_day" {
    export const isDark: false;
    export const cssClass: "ace-cloud9-day";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-cloud9_night-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-cloud9_night" {
    export const isDark: true;
    export const cssClass: "ace-cloud9-night";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-cloud9_night_low_color-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-cloud9_night_low_color" {
    export const isDark: true;
    export const cssClass: "ace-cloud9-night-low-color";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-cloud_editor-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-cloud_editor" {
    export const isDark: false;
    export const cssClass: "ace-cloud_editor";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-cloud_editor_dark-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-cloud_editor_dark" {
    export const isDark: true;
    export const cssClass: "ace-cloud_editor_dark";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-clouds-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-clouds" {
    export const isDark: false;
    export const cssClass: "ace-clouds";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-clouds_midnight-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-clouds_midnight" {
    export const isDark: true;
    export const cssClass: "ace-clouds-midnight";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-cobalt-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-cobalt" {
    export const isDark: true;
    export const cssClass: "ace-cobalt";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-crimson_editor-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-crimson_editor" {
    export const isDark: false;
    export const cssText: string;
    export const cssClass: "ace-crimson-editor";
}
declare module "ace-builds/src-noconflict/theme-dawn-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-dawn" {
    export const isDark: false;
    export const cssClass: "ace-dawn";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-dracula-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-dracula" {
    export const isDark: true;
    export const cssClass: "ace-dracula";
    export const cssText: string;
    export const $selectionColorConflict: true;
}
declare module "ace-builds/src-noconflict/theme-dreamweaver-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-dreamweaver" {
    export const isDark: false;
    export const cssClass: "ace-dreamweaver";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-eclipse-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-eclipse" {
    export const isDark: false;
    export const cssText: string;
    export const cssClass: "ace-eclipse";
}
declare module "ace-builds/src-noconflict/theme-github-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-github" {
    export const isDark: false;
    export const cssClass: "ace-github";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-github_dark-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-github_dark" {
    export const isDark: true;
    export const cssClass: "ace-github-dark";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-github_light_default-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-github_light_default" {
    export const isDark: false;
    export const cssClass: "ace-github-light-default";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-gob-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-gob" {
    export const isDark: true;
    export const cssClass: "ace-gob";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-gruvbox-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-gruvbox" {
    export const isDark: true;
    export const cssClass: "ace-gruvbox";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-gruvbox_dark_hard-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-gruvbox_dark_hard" {
    export const isDark: true;
    export const cssClass: "ace-gruvbox-dark-hard";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-gruvbox_light_hard-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-gruvbox_light_hard" {
    export const isDark: false;
    export const cssClass: "ace-gruvbox-light-hard";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-idle_fingers-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-idle_fingers" {
    export const isDark: true;
    export const cssClass: "ace-idle-fingers";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-iplastic-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-iplastic" {
    export const isDark: false;
    export const cssClass: "ace-iplastic";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-katzenmilch-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-katzenmilch" {
    export const isDark: false;
    export const cssClass: "ace-katzenmilch";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-kr_theme-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-kr_theme" {
    export const isDark: true;
    export const cssClass: "ace-kr-theme";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-kuroir-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-kuroir" {
    export const isDark: false;
    export const cssClass: "ace-kuroir";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-merbivore-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-merbivore" {
    export const isDark: true;
    export const cssClass: "ace-merbivore";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-merbivore_soft-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-merbivore_soft" {
    export const isDark: true;
    export const cssClass: "ace-merbivore-soft";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-mono_industrial-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-mono_industrial" {
    export const isDark: true;
    export const cssClass: "ace-mono-industrial";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-monokai-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-monokai" {
    export const isDark: true;
    export const cssClass: "ace-monokai";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-nord_dark-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-nord_dark" {
    export const isDark: true;
    export const cssClass: "ace-nord-dark";
    export const cssText: string;
    export const $selectionColorConflict: true;
}
declare module "ace-builds/src-noconflict/theme-one_dark-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-one_dark" {
    export const isDark: true;
    export const cssClass: "ace-one-dark";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-pastel_on_dark-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-pastel_on_dark" {
    export const isDark: true;
    export const cssClass: "ace-pastel-on-dark";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-solarized_dark-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-solarized_dark" {
    export const isDark: true;
    export const cssClass: "ace-solarized-dark";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-solarized_light-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-solarized_light" {
    export const isDark: false;
    export const cssClass: "ace-solarized-light";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-sqlserver-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-sqlserver" {
    export const isDark: false;
    export const cssClass: "ace-sqlserver";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-terminal-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-terminal" {
    export const isDark: true;
    export const cssClass: "ace-terminal-theme";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-tomorrow-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-tomorrow" {
    export const isDark: false;
    export const cssClass: "ace-tomorrow";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-tomorrow_night-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-tomorrow_night" {
    export const isDark: true;
    export const cssClass: "ace-tomorrow-night";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-tomorrow_night_blue-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-tomorrow_night_blue" {
    export const isDark: true;
    export const cssClass: "ace-tomorrow-night-blue";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-tomorrow_night_bright-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-tomorrow_night_bright" {
    export const isDark: true;
    export const cssClass: "ace-tomorrow-night-bright";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-tomorrow_night_eighties-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-tomorrow_night_eighties" {
    export const isDark: true;
    export const cssClass: "ace-tomorrow-night-eighties";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-twilight-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-twilight" {
    export const isDark: true;
    export const cssClass: "ace-twilight";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-vibrant_ink-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-vibrant_ink" {
    export const isDark: true;
    export const cssClass: "ace-vibrant-ink";
    export const cssText: string;
}
declare module "ace-builds/src-noconflict/theme-xcode-css" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/theme-xcode" {
    export const isDark: false;
    export const cssClass: "ace-xcode";
    export const cssText: string;
}
