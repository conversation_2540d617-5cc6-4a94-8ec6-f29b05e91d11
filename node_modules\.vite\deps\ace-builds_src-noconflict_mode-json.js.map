{"version": 3, "sources": ["../../ace-builds/src-noconflict/mode-json.js"], "sourcesContent": ["ace.define(\"ace/mode/json_highlight_rules\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/text_highlight_rules\"], function(require, exports, module){\"use strict\";\nvar oop = require(\"../lib/oop\");\nvar TextHighlightRules = require(\"./text_highlight_rules\").TextHighlightRules;\nvar JsonHighlightRules = function () {\n    this.$rules = {\n        \"start\": [\n            {\n                token: \"variable\", // single line\n                regex: '[\"](?:(?:\\\\\\\\.)|(?:[^\"\\\\\\\\]))*?[\"]\\\\s*(?=:)'\n            }, {\n                token: \"string\", // single line\n                regex: '\"',\n                next: \"string\"\n            }, {\n                token: \"constant.numeric\", // hex\n                regex: \"0[xX][0-9a-fA-F]+\\\\b\"\n            }, {\n                token: \"constant.numeric\", // float\n                regex: \"[+-]?\\\\d+(?:(?:\\\\.\\\\d*)?(?:[eE][+-]?\\\\d+)?)?\\\\b\"\n            }, {\n                token: \"constant.language.boolean\",\n                regex: \"(?:true|false)\\\\b\"\n            }, {\n                token: \"text\", // single quoted strings are not allowed\n                regex: \"['](?:(?:\\\\\\\\.)|(?:[^'\\\\\\\\]))*?[']\"\n            }, {\n                token: \"comment\", // comments are not allowed, but who cares?\n                regex: \"\\\\/\\\\/.*$\"\n            }, {\n                token: \"comment.start\", // comments are not allowed, but who cares?\n                regex: \"\\\\/\\\\*\",\n                next: \"comment\"\n            }, {\n                token: \"paren.lparen\",\n                regex: \"[[({]\"\n            }, {\n                token: \"paren.rparen\",\n                regex: \"[\\\\])}]\"\n            }, {\n                token: \"punctuation.operator\",\n                regex: /[,]/\n            }, {\n                token: \"text\",\n                regex: \"\\\\s+\"\n            }\n        ],\n        \"string\": [\n            {\n                token: \"constant.language.escape\",\n                regex: /\\\\(?:x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|[\"\\\\\\/bfnrt])/\n            }, {\n                token: \"string\",\n                regex: '\"|$',\n                next: \"start\"\n            }, {\n                defaultToken: \"string\"\n            }\n        ],\n        \"comment\": [\n            {\n                token: \"comment.end\", // comments are not allowed, but who cares?\n                regex: \"\\\\*\\\\/\",\n                next: \"start\"\n            }, {\n                defaultToken: \"comment\"\n            }\n        ]\n    };\n};\noop.inherits(JsonHighlightRules, TextHighlightRules);\nexports.JsonHighlightRules = JsonHighlightRules;\n\n});\n\nace.define(\"ace/mode/matching_brace_outdent\",[\"require\",\"exports\",\"module\",\"ace/range\"], function(require, exports, module){\"use strict\";\nvar Range = require(\"../range\").Range;\nvar MatchingBraceOutdent = function () { };\n(function () {\n    this.checkOutdent = function (line, input) {\n        if (!/^\\s+$/.test(line))\n            return false;\n        return /^\\s*\\}/.test(input);\n    };\n    this.autoOutdent = function (doc, row) {\n        var line = doc.getLine(row);\n        var match = line.match(/^(\\s*\\})/);\n        if (!match)\n            return 0;\n        var column = match[1].length;\n        var openBracePos = doc.findMatchingBracket({ row: row, column: column });\n        if (!openBracePos || openBracePos.row == row)\n            return 0;\n        var indent = this.$getIndent(doc.getLine(openBracePos.row));\n        doc.replace(new Range(row, 0, row, column - 1), indent);\n    };\n    this.$getIndent = function (line) {\n        return line.match(/^\\s*/)[0];\n    };\n}).call(MatchingBraceOutdent.prototype);\nexports.MatchingBraceOutdent = MatchingBraceOutdent;\n\n});\n\nace.define(\"ace/mode/folding/cstyle\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/range\",\"ace/mode/folding/fold_mode\"], function(require, exports, module){\"use strict\";\nvar oop = require(\"../../lib/oop\");\nvar Range = require(\"../../range\").Range;\nvar BaseFoldMode = require(\"./fold_mode\").FoldMode;\nvar FoldMode = exports.FoldMode = function (commentRegex) {\n    if (commentRegex) {\n        this.foldingStartMarker = new RegExp(this.foldingStartMarker.source.replace(/\\|[^|]*?$/, \"|\" + commentRegex.start));\n        this.foldingStopMarker = new RegExp(this.foldingStopMarker.source.replace(/\\|[^|]*?$/, \"|\" + commentRegex.end));\n    }\n};\noop.inherits(FoldMode, BaseFoldMode);\n(function () {\n    this.foldingStartMarker = /([\\{\\[\\(])[^\\}\\]\\)]*$|^\\s*(\\/\\*)/;\n    this.foldingStopMarker = /^[^\\[\\{\\(]*([\\}\\]\\)])|^[\\s\\*]*(\\*\\/)/;\n    this.singleLineBlockCommentRe = /^\\s*(\\/\\*).*\\*\\/\\s*$/;\n    this.tripleStarBlockCommentRe = /^\\s*(\\/\\*\\*\\*).*\\*\\/\\s*$/;\n    this.startRegionRe = /^\\s*(\\/\\*|\\/\\/)#?region\\b/;\n    this._getFoldWidgetBase = this.getFoldWidget;\n    this.getFoldWidget = function (session, foldStyle, row) {\n        var line = session.getLine(row);\n        if (this.singleLineBlockCommentRe.test(line)) {\n            if (!this.startRegionRe.test(line) && !this.tripleStarBlockCommentRe.test(line))\n                return \"\";\n        }\n        var fw = this._getFoldWidgetBase(session, foldStyle, row);\n        if (!fw && this.startRegionRe.test(line))\n            return \"start\"; // lineCommentRegionStart\n        return fw;\n    };\n    this.getFoldWidgetRange = function (session, foldStyle, row, forceMultiline) {\n        var line = session.getLine(row);\n        if (this.startRegionRe.test(line))\n            return this.getCommentRegionBlock(session, line, row);\n        var match = line.match(this.foldingStartMarker);\n        if (match) {\n            var i = match.index;\n            if (match[1])\n                return this.openingBracketBlock(session, match[1], row, i);\n            var range = session.getCommentFoldRange(row, i + match[0].length, 1);\n            if (range && !range.isMultiLine()) {\n                if (forceMultiline) {\n                    range = this.getSectionRange(session, row);\n                }\n                else if (foldStyle != \"all\")\n                    range = null;\n            }\n            return range;\n        }\n        if (foldStyle === \"markbegin\")\n            return;\n        var match = line.match(this.foldingStopMarker);\n        if (match) {\n            var i = match.index + match[0].length;\n            if (match[1])\n                return this.closingBracketBlock(session, match[1], row, i);\n            return session.getCommentFoldRange(row, i, -1);\n        }\n    };\n    this.getSectionRange = function (session, row) {\n        var line = session.getLine(row);\n        var startIndent = line.search(/\\S/);\n        var startRow = row;\n        var startColumn = line.length;\n        row = row + 1;\n        var endRow = row;\n        var maxRow = session.getLength();\n        while (++row < maxRow) {\n            line = session.getLine(row);\n            var indent = line.search(/\\S/);\n            if (indent === -1)\n                continue;\n            if (startIndent > indent)\n                break;\n            var subRange = this.getFoldWidgetRange(session, \"all\", row);\n            if (subRange) {\n                if (subRange.start.row <= startRow) {\n                    break;\n                }\n                else if (subRange.isMultiLine()) {\n                    row = subRange.end.row;\n                }\n                else if (startIndent == indent) {\n                    break;\n                }\n            }\n            endRow = row;\n        }\n        return new Range(startRow, startColumn, endRow, session.getLine(endRow).length);\n    };\n    this.getCommentRegionBlock = function (session, line, row) {\n        var startColumn = line.search(/\\s*$/);\n        var maxRow = session.getLength();\n        var startRow = row;\n        var re = /^\\s*(?:\\/\\*|\\/\\/|--)#?(end)?region\\b/;\n        var depth = 1;\n        while (++row < maxRow) {\n            line = session.getLine(row);\n            var m = re.exec(line);\n            if (!m)\n                continue;\n            if (m[1])\n                depth--;\n            else\n                depth++;\n            if (!depth)\n                break;\n        }\n        var endRow = row;\n        if (endRow > startRow) {\n            return new Range(startRow, startColumn, endRow, line.length);\n        }\n    };\n}).call(FoldMode.prototype);\n\n});\n\nace.define(\"ace/mode/json\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/text\",\"ace/mode/json_highlight_rules\",\"ace/mode/matching_brace_outdent\",\"ace/mode/folding/cstyle\",\"ace/worker/worker_client\"], function(require, exports, module){\"use strict\";\nvar oop = require(\"../lib/oop\");\nvar TextMode = require(\"./text\").Mode;\nvar HighlightRules = require(\"./json_highlight_rules\").JsonHighlightRules;\nvar MatchingBraceOutdent = require(\"./matching_brace_outdent\").MatchingBraceOutdent;\nvar CStyleFoldMode = require(\"./folding/cstyle\").FoldMode;\nvar WorkerClient = require(\"../worker/worker_client\").WorkerClient;\nvar Mode = function () {\n    this.HighlightRules = HighlightRules;\n    this.$outdent = new MatchingBraceOutdent();\n    this.$behaviour = this.$defaultBehaviour;\n    this.foldingRules = new CStyleFoldMode();\n};\noop.inherits(Mode, TextMode);\n(function () {\n    this.lineCommentStart = \"//\";\n    this.blockComment = { start: \"/*\", end: \"*/\" };\n    this.getNextLineIndent = function (state, line, tab) {\n        var indent = this.$getIndent(line);\n        if (state == \"start\") {\n            var match = line.match(/^.*[\\{\\(\\[]\\s*$/);\n            if (match) {\n                indent += tab;\n            }\n        }\n        return indent;\n    };\n    this.checkOutdent = function (state, line, input) {\n        return this.$outdent.checkOutdent(line, input);\n    };\n    this.autoOutdent = function (state, doc, row) {\n        this.$outdent.autoOutdent(doc, row);\n    };\n    this.createWorker = function (session) {\n        var worker = new WorkerClient([\"ace\"], \"ace/mode/json_worker\", \"JsonWorker\");\n        worker.attachToDocument(session.getDocument());\n        worker.on(\"annotate\", function (e) {\n            session.setAnnotations(e.data);\n        });\n        worker.on(\"terminate\", function () {\n            session.clearAnnotations();\n        });\n        return worker;\n    };\n    this.$id = \"ace/mode/json\";\n}).call(Mode.prototype);\nexports.Mode = Mode;\n\n});                (function() {\n                    ace.require([\"ace/mode/json\"], function(m) {\n                        if (typeof module == \"object\" && typeof exports == \"object\" && module) {\n                            module.exports = m;\n                        }\n                    });\n                })();\n            "], "mappings": ";;;;;AAAA;AAAA;AAAA,QAAI,OAAO,iCAAgC,CAAC,WAAU,WAAU,UAAS,eAAc,+BAA+B,GAAG,SAASA,UAASC,UAASC,SAAO;AAAC;AAC5J,UAAI,MAAMF,SAAQ,YAAY;AAC9B,UAAI,qBAAqBA,SAAQ,wBAAwB,EAAE;AAC3D,UAAI,qBAAqB,WAAY;AACjC,aAAK,SAAS;AAAA,UACV,SAAS;AAAA,YACL;AAAA,cACI,OAAO;AAAA;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA;AAAA,cACP,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YAAG;AAAA,cACC,OAAO;AAAA;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA;AAAA,cACP,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,UACJ;AAAA,UACA,UAAU;AAAA,YACN;AAAA,cACI,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YAAG;AAAA,cACC,cAAc;AAAA,YAClB;AAAA,UACJ;AAAA,UACA,WAAW;AAAA,YACP;AAAA,cACI,OAAO;AAAA;AAAA,cACP,OAAO;AAAA,cACP,MAAM;AAAA,YACV;AAAA,YAAG;AAAA,cACC,cAAc;AAAA,YAClB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,SAAS,oBAAoB,kBAAkB;AACnD,MAAAC,SAAQ,qBAAqB;AAAA,IAE7B,CAAC;AAED,QAAI,OAAO,mCAAkC,CAAC,WAAU,WAAU,UAAS,WAAW,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC;AAC5H,UAAI,QAAQF,SAAQ,UAAU,EAAE;AAChC,UAAI,uBAAuB,WAAY;AAAA,MAAE;AACzC,OAAC,WAAY;AACT,aAAK,eAAe,SAAU,MAAM,OAAO;AACvC,cAAI,CAAC,QAAQ,KAAK,IAAI;AAClB,mBAAO;AACX,iBAAO,SAAS,KAAK,KAAK;AAAA,QAC9B;AACA,aAAK,cAAc,SAAU,KAAK,KAAK;AACnC,cAAI,OAAO,IAAI,QAAQ,GAAG;AAC1B,cAAI,QAAQ,KAAK,MAAM,UAAU;AACjC,cAAI,CAAC;AACD,mBAAO;AACX,cAAI,SAAS,MAAM,CAAC,EAAE;AACtB,cAAI,eAAe,IAAI,oBAAoB,EAAE,KAAU,OAAe,CAAC;AACvE,cAAI,CAAC,gBAAgB,aAAa,OAAO;AACrC,mBAAO;AACX,cAAI,SAAS,KAAK,WAAW,IAAI,QAAQ,aAAa,GAAG,CAAC;AAC1D,cAAI,QAAQ,IAAI,MAAM,KAAK,GAAG,KAAK,SAAS,CAAC,GAAG,MAAM;AAAA,QAC1D;AACA,aAAK,aAAa,SAAU,MAAM;AAC9B,iBAAO,KAAK,MAAM,MAAM,EAAE,CAAC;AAAA,QAC/B;AAAA,MACJ,GAAG,KAAK,qBAAqB,SAAS;AACtC,MAAAC,SAAQ,uBAAuB;AAAA,IAE/B,CAAC;AAED,QAAI,OAAO,2BAA0B,CAAC,WAAU,WAAU,UAAS,eAAc,aAAY,4BAA4B,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC;AAC/J,UAAI,MAAMF,SAAQ,eAAe;AACjC,UAAI,QAAQA,SAAQ,aAAa,EAAE;AACnC,UAAI,eAAeA,SAAQ,aAAa,EAAE;AAC1C,UAAI,WAAWC,SAAQ,WAAW,SAAU,cAAc;AACtD,YAAI,cAAc;AACd,eAAK,qBAAqB,IAAI,OAAO,KAAK,mBAAmB,OAAO,QAAQ,aAAa,MAAM,aAAa,KAAK,CAAC;AAClH,eAAK,oBAAoB,IAAI,OAAO,KAAK,kBAAkB,OAAO,QAAQ,aAAa,MAAM,aAAa,GAAG,CAAC;AAAA,QAClH;AAAA,MACJ;AACA,UAAI,SAAS,UAAU,YAAY;AACnC,OAAC,WAAY;AACT,aAAK,qBAAqB;AAC1B,aAAK,oBAAoB;AACzB,aAAK,2BAA2B;AAChC,aAAK,2BAA2B;AAChC,aAAK,gBAAgB;AACrB,aAAK,qBAAqB,KAAK;AAC/B,aAAK,gBAAgB,SAAU,SAAS,WAAW,KAAK;AACpD,cAAI,OAAO,QAAQ,QAAQ,GAAG;AAC9B,cAAI,KAAK,yBAAyB,KAAK,IAAI,GAAG;AAC1C,gBAAI,CAAC,KAAK,cAAc,KAAK,IAAI,KAAK,CAAC,KAAK,yBAAyB,KAAK,IAAI;AAC1E,qBAAO;AAAA,UACf;AACA,cAAI,KAAK,KAAK,mBAAmB,SAAS,WAAW,GAAG;AACxD,cAAI,CAAC,MAAM,KAAK,cAAc,KAAK,IAAI;AACnC,mBAAO;AACX,iBAAO;AAAA,QACX;AACA,aAAK,qBAAqB,SAAU,SAAS,WAAW,KAAK,gBAAgB;AACzE,cAAI,OAAO,QAAQ,QAAQ,GAAG;AAC9B,cAAI,KAAK,cAAc,KAAK,IAAI;AAC5B,mBAAO,KAAK,sBAAsB,SAAS,MAAM,GAAG;AACxD,cAAI,QAAQ,KAAK,MAAM,KAAK,kBAAkB;AAC9C,cAAI,OAAO;AACP,gBAAI,IAAI,MAAM;AACd,gBAAI,MAAM,CAAC;AACP,qBAAO,KAAK,oBAAoB,SAAS,MAAM,CAAC,GAAG,KAAK,CAAC;AAC7D,gBAAI,QAAQ,QAAQ,oBAAoB,KAAK,IAAI,MAAM,CAAC,EAAE,QAAQ,CAAC;AACnE,gBAAI,SAAS,CAAC,MAAM,YAAY,GAAG;AAC/B,kBAAI,gBAAgB;AAChB,wBAAQ,KAAK,gBAAgB,SAAS,GAAG;AAAA,cAC7C,WACS,aAAa;AAClB,wBAAQ;AAAA,YAChB;AACA,mBAAO;AAAA,UACX;AACA,cAAI,cAAc;AACd;AACJ,cAAI,QAAQ,KAAK,MAAM,KAAK,iBAAiB;AAC7C,cAAI,OAAO;AACP,gBAAI,IAAI,MAAM,QAAQ,MAAM,CAAC,EAAE;AAC/B,gBAAI,MAAM,CAAC;AACP,qBAAO,KAAK,oBAAoB,SAAS,MAAM,CAAC,GAAG,KAAK,CAAC;AAC7D,mBAAO,QAAQ,oBAAoB,KAAK,GAAG,EAAE;AAAA,UACjD;AAAA,QACJ;AACA,aAAK,kBAAkB,SAAU,SAAS,KAAK;AAC3C,cAAI,OAAO,QAAQ,QAAQ,GAAG;AAC9B,cAAI,cAAc,KAAK,OAAO,IAAI;AAClC,cAAI,WAAW;AACf,cAAI,cAAc,KAAK;AACvB,gBAAM,MAAM;AACZ,cAAI,SAAS;AACb,cAAI,SAAS,QAAQ,UAAU;AAC/B,iBAAO,EAAE,MAAM,QAAQ;AACnB,mBAAO,QAAQ,QAAQ,GAAG;AAC1B,gBAAI,SAAS,KAAK,OAAO,IAAI;AAC7B,gBAAI,WAAW;AACX;AACJ,gBAAI,cAAc;AACd;AACJ,gBAAI,WAAW,KAAK,mBAAmB,SAAS,OAAO,GAAG;AAC1D,gBAAI,UAAU;AACV,kBAAI,SAAS,MAAM,OAAO,UAAU;AAChC;AAAA,cACJ,WACS,SAAS,YAAY,GAAG;AAC7B,sBAAM,SAAS,IAAI;AAAA,cACvB,WACS,eAAe,QAAQ;AAC5B;AAAA,cACJ;AAAA,YACJ;AACA,qBAAS;AAAA,UACb;AACA,iBAAO,IAAI,MAAM,UAAU,aAAa,QAAQ,QAAQ,QAAQ,MAAM,EAAE,MAAM;AAAA,QAClF;AACA,aAAK,wBAAwB,SAAU,SAAS,MAAM,KAAK;AACvD,cAAI,cAAc,KAAK,OAAO,MAAM;AACpC,cAAI,SAAS,QAAQ,UAAU;AAC/B,cAAI,WAAW;AACf,cAAI,KAAK;AACT,cAAI,QAAQ;AACZ,iBAAO,EAAE,MAAM,QAAQ;AACnB,mBAAO,QAAQ,QAAQ,GAAG;AAC1B,gBAAI,IAAI,GAAG,KAAK,IAAI;AACpB,gBAAI,CAAC;AACD;AACJ,gBAAI,EAAE,CAAC;AACH;AAAA;AAEA;AACJ,gBAAI,CAAC;AACD;AAAA,UACR;AACA,cAAI,SAAS;AACb,cAAI,SAAS,UAAU;AACnB,mBAAO,IAAI,MAAM,UAAU,aAAa,QAAQ,KAAK,MAAM;AAAA,UAC/D;AAAA,QACJ;AAAA,MACJ,GAAG,KAAK,SAAS,SAAS;AAAA,IAE1B,CAAC;AAED,QAAI,OAAO,iBAAgB,CAAC,WAAU,WAAU,UAAS,eAAc,iBAAgB,iCAAgC,mCAAkC,2BAA0B,0BAA0B,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC;AACnP,UAAI,MAAMF,SAAQ,YAAY;AAC9B,UAAI,WAAWA,SAAQ,QAAQ,EAAE;AACjC,UAAI,iBAAiBA,SAAQ,wBAAwB,EAAE;AACvD,UAAI,uBAAuBA,SAAQ,0BAA0B,EAAE;AAC/D,UAAI,iBAAiBA,SAAQ,kBAAkB,EAAE;AACjD,UAAI,eAAeA,SAAQ,yBAAyB,EAAE;AACtD,UAAI,OAAO,WAAY;AACnB,aAAK,iBAAiB;AACtB,aAAK,WAAW,IAAI,qBAAqB;AACzC,aAAK,aAAa,KAAK;AACvB,aAAK,eAAe,IAAI,eAAe;AAAA,MAC3C;AACA,UAAI,SAAS,MAAM,QAAQ;AAC3B,OAAC,WAAY;AACT,aAAK,mBAAmB;AACxB,aAAK,eAAe,EAAE,OAAO,MAAM,KAAK,KAAK;AAC7C,aAAK,oBAAoB,SAAU,OAAO,MAAM,KAAK;AACjD,cAAI,SAAS,KAAK,WAAW,IAAI;AACjC,cAAI,SAAS,SAAS;AAClB,gBAAI,QAAQ,KAAK,MAAM,iBAAiB;AACxC,gBAAI,OAAO;AACP,wBAAU;AAAA,YACd;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AACA,aAAK,eAAe,SAAU,OAAO,MAAM,OAAO;AAC9C,iBAAO,KAAK,SAAS,aAAa,MAAM,KAAK;AAAA,QACjD;AACA,aAAK,cAAc,SAAU,OAAO,KAAK,KAAK;AAC1C,eAAK,SAAS,YAAY,KAAK,GAAG;AAAA,QACtC;AACA,aAAK,eAAe,SAAU,SAAS;AACnC,cAAI,SAAS,IAAI,aAAa,CAAC,KAAK,GAAG,wBAAwB,YAAY;AAC3E,iBAAO,iBAAiB,QAAQ,YAAY,CAAC;AAC7C,iBAAO,GAAG,YAAY,SAAU,GAAG;AAC/B,oBAAQ,eAAe,EAAE,IAAI;AAAA,UACjC,CAAC;AACD,iBAAO,GAAG,aAAa,WAAY;AAC/B,oBAAQ,iBAAiB;AAAA,UAC7B,CAAC;AACD,iBAAO;AAAA,QACX;AACA,aAAK,MAAM;AAAA,MACf,GAAG,KAAK,KAAK,SAAS;AACtB,MAAAC,SAAQ,OAAO;AAAA,IAEf,CAAC;AAAkB,KAAC,WAAW;AACX,UAAI,QAAQ,CAAC,eAAe,GAAG,SAAS,GAAG;AACvC,YAAI,OAAO,UAAU,YAAY,OAAO,WAAW,YAAY,QAAQ;AACnE,iBAAO,UAAU;AAAA,QACrB;AAAA,MACJ,CAAC;AAAA,IACL,GAAG;AAAA;AAAA;", "names": ["require", "exports", "module"]}