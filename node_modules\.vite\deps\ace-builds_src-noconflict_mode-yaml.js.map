{"version": 3, "sources": ["../../ace-builds/src-noconflict/mode-yaml.js"], "sourcesContent": ["ace.define(\"ace/mode/yaml_highlight_rules\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/text_highlight_rules\"], function(require, exports, module){\"use strict\";\nvar oop = require(\"../lib/oop\");\nvar TextHighlightRules = require(\"./text_highlight_rules\").TextHighlightRules;\nvar YamlHighlightRules = function () {\n    this.$rules = {\n        \"start\": [\n            {\n                token: \"comment\",\n                regex: \"#.*$\"\n            }, {\n                token: \"list.markup\",\n                regex: /^(?:-{3}|\\.{3})\\s*(?=#|$)/\n            }, {\n                token: \"list.markup\",\n                regex: /^\\s*[\\-?](?:$|\\s)/\n            }, {\n                token: \"constant\",\n                regex: \"!![\\\\w//]+\"\n            }, {\n                token: \"constant.language\",\n                regex: \"[&\\\\*][a-zA-Z0-9-_]+\"\n            }, {\n                token: [\"meta.tag\", \"keyword\"],\n                regex: /^(\\s*\\w[^\\s:]*?)(:(?=\\s|$))/\n            }, {\n                token: [\"meta.tag\", \"keyword\"],\n                regex: /(\\w[^\\s:]*?)(\\s*:(?=\\s|$))/\n            }, {\n                token: \"keyword.operator\",\n                regex: \"<<\\\\w*:\\\\w*\"\n            }, {\n                token: \"keyword.operator\",\n                regex: \"-\\\\s*(?=[{])\"\n            }, {\n                token: \"string\", // single line\n                regex: '[\"](?:(?:\\\\\\\\.)|(?:[^\"\\\\\\\\]))*?[\"]'\n            }, {\n                token: \"string\", // multi line string start\n                regex: /[|>][-+\\d]*(?:$|\\s+(?:$|#))/,\n                onMatch: function (val, state, stack, line) {\n                    line = line.replace(/ #.*/, \"\");\n                    var indent = /^ *((:\\s*)?-(\\s*[^|>])?)?/.exec(line)[0]\n                        .replace(/\\S\\s*$/, \"\").length;\n                    var indentationIndicator = parseInt(/\\d+[\\s+-]*$/.exec(line));\n                    if (indentationIndicator) {\n                        indent += indentationIndicator - 1;\n                        this.next = \"mlString\";\n                    }\n                    else {\n                        this.next = \"mlStringPre\";\n                    }\n                    if (!stack.length) {\n                        stack.push(this.next);\n                        stack.push(indent);\n                    }\n                    else {\n                        stack[0] = this.next;\n                        stack[1] = indent;\n                    }\n                    return this.token;\n                },\n                next: \"mlString\"\n            }, {\n                token: \"string\", // single quoted string\n                regex: \"['](?:(?:\\\\\\\\.)|(?:[^'\\\\\\\\]))*?[']\"\n            }, {\n                token: \"constant.numeric\", // float\n                regex: /(\\b|[+\\-\\.])[\\d_]+(?:(?:\\.[\\d_]*)?(?:[eE][+\\-]?[\\d_]+)?)(?=[^\\d-\\w]|$)$/\n            }, {\n                token: \"constant.numeric\", // other number\n                regex: /[+\\-]?\\.inf\\b|NaN\\b|0x[\\dA-Fa-f_]+|0b[10_]+/\n            }, {\n                token: \"constant.language.boolean\",\n                regex: \"\\\\b(?:true|false|TRUE|FALSE|True|False|yes|no)\\\\b\"\n            }, {\n                token: \"paren.lparen\",\n                regex: \"[[({]\"\n            }, {\n                token: \"paren.rparen\",\n                regex: \"[\\\\])}]\"\n            }, {\n                token: \"text\",\n                regex: /[^\\s,:\\[\\]\\{\\}]+/\n            }\n        ],\n        \"mlStringPre\": [\n            {\n                token: \"indent\",\n                regex: /^ *$/\n            }, {\n                token: \"indent\",\n                regex: /^ */,\n                onMatch: function (val, state, stack) {\n                    var curIndent = stack[1];\n                    if (curIndent >= val.length) {\n                        this.next = \"start\";\n                        stack.shift();\n                        stack.shift();\n                    }\n                    else {\n                        stack[1] = val.length - 1;\n                        this.next = stack[0] = \"mlString\";\n                    }\n                    return this.token;\n                },\n                next: \"mlString\"\n            }, {\n                defaultToken: \"string\"\n            }\n        ],\n        \"mlString\": [\n            {\n                token: \"indent\",\n                regex: /^ *$/\n            }, {\n                token: \"indent\",\n                regex: /^ */,\n                onMatch: function (val, state, stack) {\n                    var curIndent = stack[1];\n                    if (curIndent >= val.length) {\n                        this.next = \"start\";\n                        stack.splice(0);\n                    }\n                    else {\n                        this.next = \"mlString\";\n                    }\n                    return this.token;\n                },\n                next: \"mlString\"\n            }, {\n                token: \"string\",\n                regex: '.+'\n            }\n        ]\n    };\n    this.normalizeRules();\n};\noop.inherits(YamlHighlightRules, TextHighlightRules);\nexports.YamlHighlightRules = YamlHighlightRules;\n\n});\n\nace.define(\"ace/mode/matching_brace_outdent\",[\"require\",\"exports\",\"module\",\"ace/range\"], function(require, exports, module){\"use strict\";\nvar Range = require(\"../range\").Range;\nvar MatchingBraceOutdent = function () { };\n(function () {\n    this.checkOutdent = function (line, input) {\n        if (!/^\\s+$/.test(line))\n            return false;\n        return /^\\s*\\}/.test(input);\n    };\n    this.autoOutdent = function (doc, row) {\n        var line = doc.getLine(row);\n        var match = line.match(/^(\\s*\\})/);\n        if (!match)\n            return 0;\n        var column = match[1].length;\n        var openBracePos = doc.findMatchingBracket({ row: row, column: column });\n        if (!openBracePos || openBracePos.row == row)\n            return 0;\n        var indent = this.$getIndent(doc.getLine(openBracePos.row));\n        doc.replace(new Range(row, 0, row, column - 1), indent);\n    };\n    this.$getIndent = function (line) {\n        return line.match(/^\\s*/)[0];\n    };\n}).call(MatchingBraceOutdent.prototype);\nexports.MatchingBraceOutdent = MatchingBraceOutdent;\n\n});\n\nace.define(\"ace/mode/folding/coffee\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/folding/fold_mode\",\"ace/range\"], function(require, exports, module){\"use strict\";\nvar oop = require(\"../../lib/oop\");\nvar BaseFoldMode = require(\"./fold_mode\").FoldMode;\nvar Range = require(\"../../range\").Range;\nvar FoldMode = exports.FoldMode = function () { };\noop.inherits(FoldMode, BaseFoldMode);\n(function () {\n    this.commentBlock = function (session, row) {\n        var re = /\\S/;\n        var line = session.getLine(row);\n        var startLevel = line.search(re);\n        if (startLevel == -1 || line[startLevel] != \"#\")\n            return;\n        var startColumn = line.length;\n        var maxRow = session.getLength();\n        var startRow = row;\n        var endRow = row;\n        while (++row < maxRow) {\n            line = session.getLine(row);\n            var level = line.search(re);\n            if (level == -1)\n                continue;\n            if (line[level] != \"#\")\n                break;\n            endRow = row;\n        }\n        if (endRow > startRow) {\n            var endColumn = session.getLine(endRow).length;\n            return new Range(startRow, startColumn, endRow, endColumn);\n        }\n    };\n    this.getFoldWidgetRange = function (session, foldStyle, row) {\n        var range = this.indentationBlock(session, row);\n        if (range)\n            return range;\n        range = this.commentBlock(session, row);\n        if (range)\n            return range;\n    };\n    this.getFoldWidget = function (session, foldStyle, row) {\n        var line = session.getLine(row);\n        var indent = line.search(/\\S/);\n        var next = session.getLine(row + 1);\n        var prev = session.getLine(row - 1);\n        var prevIndent = prev.search(/\\S/);\n        var nextIndent = next.search(/\\S/);\n        if (indent == -1) {\n            session.foldWidgets[row - 1] = prevIndent != -1 && prevIndent < nextIndent ? \"start\" : \"\";\n            return \"\";\n        }\n        if (prevIndent == -1) {\n            if (indent == nextIndent && line[indent] == \"#\" && next[indent] == \"#\") {\n                session.foldWidgets[row - 1] = \"\";\n                session.foldWidgets[row + 1] = \"\";\n                return \"start\";\n            }\n        }\n        else if (prevIndent == indent && line[indent] == \"#\" && prev[indent] == \"#\") {\n            if (session.getLine(row - 2).search(/\\S/) == -1) {\n                session.foldWidgets[row - 1] = \"start\";\n                session.foldWidgets[row + 1] = \"\";\n                return \"\";\n            }\n        }\n        if (prevIndent != -1 && prevIndent < indent)\n            session.foldWidgets[row - 1] = \"start\";\n        else\n            session.foldWidgets[row - 1] = \"\";\n        if (indent < nextIndent)\n            return \"start\";\n        else\n            return \"\";\n    };\n}).call(FoldMode.prototype);\n\n});\n\nace.define(\"ace/mode/folding/yaml\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/folding/coffee\",\"ace/range\"], function(require, exports, module){\"use strict\";\nvar oop = require(\"../../lib/oop\");\nvar CoffeeFoldMode = require(\"./coffee\").FoldMode;\nvar Range = require(\"../../range\").Range;\nvar FoldMode = exports.FoldMode = function () { };\noop.inherits(FoldMode, CoffeeFoldMode);\n(function () {\n    this.getFoldWidgetRange = function (session, foldStyle, row) {\n        var re = /\\S/;\n        var line = session.getLine(row);\n        var startLevel = line.search(re);\n        var isCommentFold = line[startLevel] === \"#\";\n        var isDashFold = line[startLevel] === \"-\";\n        if (startLevel == -1)\n            return;\n        var startColumn = line.length;\n        var maxRow = session.getLength();\n        var startRow = row;\n        var endRow = row;\n        if (isCommentFold) {\n            var range = this.commentBlock(session, row);\n            if (range)\n                return range;\n        }\n        else if (isDashFold) {\n            var range = this.indentationBlock(session, row);\n            if (range)\n                return range;\n        }\n        else {\n            while (++row < maxRow) {\n                var line = session.getLine(row);\n                var level = line.search(re);\n                if (level == -1)\n                    continue;\n                if (level <= startLevel && line[startLevel] !== '-') {\n                    var token = session.getTokenAt(row, 0);\n                    if (!token || token.type !== \"string\")\n                        break;\n                }\n                endRow = row;\n            }\n        }\n        if (endRow > startRow) {\n            var endColumn = session.getLine(endRow).length;\n            return new Range(startRow, startColumn, endRow, endColumn);\n        }\n    };\n    this.getFoldWidget = function (session, foldStyle, row) {\n        var line = session.getLine(row);\n        var indent = line.search(/\\S/);\n        var next = session.getLine(row + 1);\n        var prev = session.getLine(row - 1);\n        var prevIndent = prev.search(/\\S/);\n        var nextIndent = next.search(/\\S/);\n        var lineStartsWithDash = line[indent] === '-';\n        if (indent == -1) {\n            session.foldWidgets[row - 1] = prevIndent != -1 && prevIndent < nextIndent ? \"start\" : \"\";\n            return \"\";\n        }\n        if (prevIndent == -1) {\n            if (indent == nextIndent && line[indent] == \"#\" && next[indent] == \"#\") {\n                session.foldWidgets[row - 1] = \"\";\n                session.foldWidgets[row + 1] = \"\";\n                return \"start\";\n            }\n        }\n        else if (prevIndent == indent && line[indent] == \"#\" && prev[indent] == \"#\") {\n            if (session.getLine(row - 2).search(/\\S/) == -1) {\n                session.foldWidgets[row - 1] = \"start\";\n                session.foldWidgets[row + 1] = \"\";\n                return \"\";\n            }\n        }\n        if (prevIndent != -1 && prevIndent < indent) {\n            session.foldWidgets[row - 1] = \"start\";\n        }\n        else if (prevIndent != -1 && (prevIndent == indent && lineStartsWithDash)) {\n            session.foldWidgets[row - 1] = \"start\";\n        }\n        else {\n            session.foldWidgets[row - 1] = \"\";\n        }\n        if (indent < nextIndent)\n            return \"start\";\n        else\n            return \"\";\n    };\n}).call(FoldMode.prototype);\n\n});\n\nace.define(\"ace/mode/yaml\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/text\",\"ace/mode/yaml_highlight_rules\",\"ace/mode/matching_brace_outdent\",\"ace/mode/folding/yaml\",\"ace/worker/worker_client\"], function(require, exports, module){\"use strict\";\nvar oop = require(\"../lib/oop\");\nvar TextMode = require(\"./text\").Mode;\nvar YamlHighlightRules = require(\"./yaml_highlight_rules\").YamlHighlightRules;\nvar MatchingBraceOutdent = require(\"./matching_brace_outdent\").MatchingBraceOutdent;\nvar FoldMode = require(\"./folding/yaml\").FoldMode;\nvar WorkerClient = require(\"../worker/worker_client\").WorkerClient;\nvar Mode = function () {\n    this.HighlightRules = YamlHighlightRules;\n    this.$outdent = new MatchingBraceOutdent();\n    this.foldingRules = new FoldMode();\n    this.$behaviour = this.$defaultBehaviour;\n};\noop.inherits(Mode, TextMode);\n(function () {\n    this.lineCommentStart = [\"#\"];\n    this.getNextLineIndent = function (state, line, tab) {\n        var indent = this.$getIndent(line);\n        if (state == \"start\") {\n            var match = line.match(/^.*[\\{\\(\\[]\\s*$/);\n            if (match) {\n                indent += tab;\n            }\n        }\n        return indent;\n    };\n    this.checkOutdent = function (state, line, input) {\n        return this.$outdent.checkOutdent(line, input);\n    };\n    this.autoOutdent = function (state, doc, row) {\n        this.$outdent.autoOutdent(doc, row);\n    };\n    this.createWorker = function (session) {\n        var worker = new WorkerClient([\"ace\"], \"ace/mode/yaml_worker\", \"YamlWorker\");\n        worker.attachToDocument(session.getDocument());\n        worker.on(\"annotate\", function (results) {\n            session.setAnnotations(results.data);\n        });\n        worker.on(\"terminate\", function () {\n            session.clearAnnotations();\n        });\n        return worker;\n    };\n    this.$id = \"ace/mode/yaml\";\n}).call(Mode.prototype);\nexports.Mode = Mode;\n\n});                (function() {\n                    ace.require([\"ace/mode/yaml\"], function(m) {\n                        if (typeof module == \"object\" && typeof exports == \"object\" && module) {\n                            module.exports = m;\n                        }\n                    });\n                })();\n            "], "mappings": ";;;;;AAAA;AAAA;AAAA,QAAI,OAAO,iCAAgC,CAAC,WAAU,WAAU,UAAS,eAAc,+BAA+B,GAAG,SAASA,UAASC,UAASC,SAAO;AAAC;AAC5J,UAAI,MAAMF,SAAQ,YAAY;AAC9B,UAAI,qBAAqBA,SAAQ,wBAAwB,EAAE;AAC3D,UAAI,qBAAqB,WAAY;AACjC,aAAK,SAAS;AAAA,UACV,SAAS;AAAA,YACL;AAAA,cACI,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO,CAAC,YAAY,SAAS;AAAA,cAC7B,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO,CAAC,YAAY,SAAS;AAAA,cAC7B,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA;AAAA,cACP,OAAO;AAAA,cACP,SAAS,SAAU,KAAK,OAAO,OAAO,MAAM;AACxC,uBAAO,KAAK,QAAQ,QAAQ,EAAE;AAC9B,oBAAI,SAAS,4BAA4B,KAAK,IAAI,EAAE,CAAC,EAChD,QAAQ,UAAU,EAAE,EAAE;AAC3B,oBAAI,uBAAuB,SAAS,cAAc,KAAK,IAAI,CAAC;AAC5D,oBAAI,sBAAsB;AACtB,4BAAU,uBAAuB;AACjC,uBAAK,OAAO;AAAA,gBAChB,OACK;AACD,uBAAK,OAAO;AAAA,gBAChB;AACA,oBAAI,CAAC,MAAM,QAAQ;AACf,wBAAM,KAAK,KAAK,IAAI;AACpB,wBAAM,KAAK,MAAM;AAAA,gBACrB,OACK;AACD,wBAAM,CAAC,IAAI,KAAK;AAChB,wBAAM,CAAC,IAAI;AAAA,gBACf;AACA,uBAAO,KAAK;AAAA,cAChB;AAAA,cACA,MAAM;AAAA,YACV;AAAA,YAAG;AAAA,cACC,OAAO;AAAA;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,UACJ;AAAA,UACA,eAAe;AAAA,YACX;AAAA,cACI,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,cACP,SAAS,SAAU,KAAK,OAAO,OAAO;AAClC,oBAAI,YAAY,MAAM,CAAC;AACvB,oBAAI,aAAa,IAAI,QAAQ;AACzB,uBAAK,OAAO;AACZ,wBAAM,MAAM;AACZ,wBAAM,MAAM;AAAA,gBAChB,OACK;AACD,wBAAM,CAAC,IAAI,IAAI,SAAS;AACxB,uBAAK,OAAO,MAAM,CAAC,IAAI;AAAA,gBAC3B;AACA,uBAAO,KAAK;AAAA,cAChB;AAAA,cACA,MAAM;AAAA,YACV;AAAA,YAAG;AAAA,cACC,cAAc;AAAA,YAClB;AAAA,UACJ;AAAA,UACA,YAAY;AAAA,YACR;AAAA,cACI,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,cACP,SAAS,SAAU,KAAK,OAAO,OAAO;AAClC,oBAAI,YAAY,MAAM,CAAC;AACvB,oBAAI,aAAa,IAAI,QAAQ;AACzB,uBAAK,OAAO;AACZ,wBAAM,OAAO,CAAC;AAAA,gBAClB,OACK;AACD,uBAAK,OAAO;AAAA,gBAChB;AACA,uBAAO,KAAK;AAAA,cAChB;AAAA,cACA,MAAM;AAAA,YACV;AAAA,YAAG;AAAA,cACC,OAAO;AAAA,cACP,OAAO;AAAA,YACX;AAAA,UACJ;AAAA,QACJ;AACA,aAAK,eAAe;AAAA,MACxB;AACA,UAAI,SAAS,oBAAoB,kBAAkB;AACnD,MAAAC,SAAQ,qBAAqB;AAAA,IAE7B,CAAC;AAED,QAAI,OAAO,mCAAkC,CAAC,WAAU,WAAU,UAAS,WAAW,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC;AAC5H,UAAI,QAAQF,SAAQ,UAAU,EAAE;AAChC,UAAI,uBAAuB,WAAY;AAAA,MAAE;AACzC,OAAC,WAAY;AACT,aAAK,eAAe,SAAU,MAAM,OAAO;AACvC,cAAI,CAAC,QAAQ,KAAK,IAAI;AAClB,mBAAO;AACX,iBAAO,SAAS,KAAK,KAAK;AAAA,QAC9B;AACA,aAAK,cAAc,SAAU,KAAK,KAAK;AACnC,cAAI,OAAO,IAAI,QAAQ,GAAG;AAC1B,cAAI,QAAQ,KAAK,MAAM,UAAU;AACjC,cAAI,CAAC;AACD,mBAAO;AACX,cAAI,SAAS,MAAM,CAAC,EAAE;AACtB,cAAI,eAAe,IAAI,oBAAoB,EAAE,KAAU,OAAe,CAAC;AACvE,cAAI,CAAC,gBAAgB,aAAa,OAAO;AACrC,mBAAO;AACX,cAAI,SAAS,KAAK,WAAW,IAAI,QAAQ,aAAa,GAAG,CAAC;AAC1D,cAAI,QAAQ,IAAI,MAAM,KAAK,GAAG,KAAK,SAAS,CAAC,GAAG,MAAM;AAAA,QAC1D;AACA,aAAK,aAAa,SAAU,MAAM;AAC9B,iBAAO,KAAK,MAAM,MAAM,EAAE,CAAC;AAAA,QAC/B;AAAA,MACJ,GAAG,KAAK,qBAAqB,SAAS;AACtC,MAAAC,SAAQ,uBAAuB;AAAA,IAE/B,CAAC;AAED,QAAI,OAAO,2BAA0B,CAAC,WAAU,WAAU,UAAS,eAAc,8BAA6B,WAAW,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC;AAC/J,UAAI,MAAMF,SAAQ,eAAe;AACjC,UAAI,eAAeA,SAAQ,aAAa,EAAE;AAC1C,UAAI,QAAQA,SAAQ,aAAa,EAAE;AACnC,UAAI,WAAWC,SAAQ,WAAW,WAAY;AAAA,MAAE;AAChD,UAAI,SAAS,UAAU,YAAY;AACnC,OAAC,WAAY;AACT,aAAK,eAAe,SAAU,SAAS,KAAK;AACxC,cAAI,KAAK;AACT,cAAI,OAAO,QAAQ,QAAQ,GAAG;AAC9B,cAAI,aAAa,KAAK,OAAO,EAAE;AAC/B,cAAI,cAAc,MAAM,KAAK,UAAU,KAAK;AACxC;AACJ,cAAI,cAAc,KAAK;AACvB,cAAI,SAAS,QAAQ,UAAU;AAC/B,cAAI,WAAW;AACf,cAAI,SAAS;AACb,iBAAO,EAAE,MAAM,QAAQ;AACnB,mBAAO,QAAQ,QAAQ,GAAG;AAC1B,gBAAI,QAAQ,KAAK,OAAO,EAAE;AAC1B,gBAAI,SAAS;AACT;AACJ,gBAAI,KAAK,KAAK,KAAK;AACf;AACJ,qBAAS;AAAA,UACb;AACA,cAAI,SAAS,UAAU;AACnB,gBAAI,YAAY,QAAQ,QAAQ,MAAM,EAAE;AACxC,mBAAO,IAAI,MAAM,UAAU,aAAa,QAAQ,SAAS;AAAA,UAC7D;AAAA,QACJ;AACA,aAAK,qBAAqB,SAAU,SAAS,WAAW,KAAK;AACzD,cAAI,QAAQ,KAAK,iBAAiB,SAAS,GAAG;AAC9C,cAAI;AACA,mBAAO;AACX,kBAAQ,KAAK,aAAa,SAAS,GAAG;AACtC,cAAI;AACA,mBAAO;AAAA,QACf;AACA,aAAK,gBAAgB,SAAU,SAAS,WAAW,KAAK;AACpD,cAAI,OAAO,QAAQ,QAAQ,GAAG;AAC9B,cAAI,SAAS,KAAK,OAAO,IAAI;AAC7B,cAAI,OAAO,QAAQ,QAAQ,MAAM,CAAC;AAClC,cAAI,OAAO,QAAQ,QAAQ,MAAM,CAAC;AAClC,cAAI,aAAa,KAAK,OAAO,IAAI;AACjC,cAAI,aAAa,KAAK,OAAO,IAAI;AACjC,cAAI,UAAU,IAAI;AACd,oBAAQ,YAAY,MAAM,CAAC,IAAI,cAAc,MAAM,aAAa,aAAa,UAAU;AACvF,mBAAO;AAAA,UACX;AACA,cAAI,cAAc,IAAI;AAClB,gBAAI,UAAU,cAAc,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,KAAK;AACpE,sBAAQ,YAAY,MAAM,CAAC,IAAI;AAC/B,sBAAQ,YAAY,MAAM,CAAC,IAAI;AAC/B,qBAAO;AAAA,YACX;AAAA,UACJ,WACS,cAAc,UAAU,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,KAAK;AACzE,gBAAI,QAAQ,QAAQ,MAAM,CAAC,EAAE,OAAO,IAAI,KAAK,IAAI;AAC7C,sBAAQ,YAAY,MAAM,CAAC,IAAI;AAC/B,sBAAQ,YAAY,MAAM,CAAC,IAAI;AAC/B,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,cAAI,cAAc,MAAM,aAAa;AACjC,oBAAQ,YAAY,MAAM,CAAC,IAAI;AAAA;AAE/B,oBAAQ,YAAY,MAAM,CAAC,IAAI;AACnC,cAAI,SAAS;AACT,mBAAO;AAAA;AAEP,mBAAO;AAAA,QACf;AAAA,MACJ,GAAG,KAAK,SAAS,SAAS;AAAA,IAE1B,CAAC;AAED,QAAI,OAAO,yBAAwB,CAAC,WAAU,WAAU,UAAS,eAAc,2BAA0B,WAAW,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC;AAC1J,UAAI,MAAMF,SAAQ,eAAe;AACjC,UAAI,iBAAiBA,SAAQ,UAAU,EAAE;AACzC,UAAI,QAAQA,SAAQ,aAAa,EAAE;AACnC,UAAI,WAAWC,SAAQ,WAAW,WAAY;AAAA,MAAE;AAChD,UAAI,SAAS,UAAU,cAAc;AACrC,OAAC,WAAY;AACT,aAAK,qBAAqB,SAAU,SAAS,WAAW,KAAK;AACzD,cAAI,KAAK;AACT,cAAI,OAAO,QAAQ,QAAQ,GAAG;AAC9B,cAAI,aAAa,KAAK,OAAO,EAAE;AAC/B,cAAI,gBAAgB,KAAK,UAAU,MAAM;AACzC,cAAI,aAAa,KAAK,UAAU,MAAM;AACtC,cAAI,cAAc;AACd;AACJ,cAAI,cAAc,KAAK;AACvB,cAAI,SAAS,QAAQ,UAAU;AAC/B,cAAI,WAAW;AACf,cAAI,SAAS;AACb,cAAI,eAAe;AACf,gBAAI,QAAQ,KAAK,aAAa,SAAS,GAAG;AAC1C,gBAAI;AACA,qBAAO;AAAA,UACf,WACS,YAAY;AACjB,gBAAI,QAAQ,KAAK,iBAAiB,SAAS,GAAG;AAC9C,gBAAI;AACA,qBAAO;AAAA,UACf,OACK;AACD,mBAAO,EAAE,MAAM,QAAQ;AACnB,kBAAI,OAAO,QAAQ,QAAQ,GAAG;AAC9B,kBAAI,QAAQ,KAAK,OAAO,EAAE;AAC1B,kBAAI,SAAS;AACT;AACJ,kBAAI,SAAS,cAAc,KAAK,UAAU,MAAM,KAAK;AACjD,oBAAI,QAAQ,QAAQ,WAAW,KAAK,CAAC;AACrC,oBAAI,CAAC,SAAS,MAAM,SAAS;AACzB;AAAA,cACR;AACA,uBAAS;AAAA,YACb;AAAA,UACJ;AACA,cAAI,SAAS,UAAU;AACnB,gBAAI,YAAY,QAAQ,QAAQ,MAAM,EAAE;AACxC,mBAAO,IAAI,MAAM,UAAU,aAAa,QAAQ,SAAS;AAAA,UAC7D;AAAA,QACJ;AACA,aAAK,gBAAgB,SAAU,SAAS,WAAW,KAAK;AACpD,cAAI,OAAO,QAAQ,QAAQ,GAAG;AAC9B,cAAI,SAAS,KAAK,OAAO,IAAI;AAC7B,cAAI,OAAO,QAAQ,QAAQ,MAAM,CAAC;AAClC,cAAI,OAAO,QAAQ,QAAQ,MAAM,CAAC;AAClC,cAAI,aAAa,KAAK,OAAO,IAAI;AACjC,cAAI,aAAa,KAAK,OAAO,IAAI;AACjC,cAAI,qBAAqB,KAAK,MAAM,MAAM;AAC1C,cAAI,UAAU,IAAI;AACd,oBAAQ,YAAY,MAAM,CAAC,IAAI,cAAc,MAAM,aAAa,aAAa,UAAU;AACvF,mBAAO;AAAA,UACX;AACA,cAAI,cAAc,IAAI;AAClB,gBAAI,UAAU,cAAc,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,KAAK;AACpE,sBAAQ,YAAY,MAAM,CAAC,IAAI;AAC/B,sBAAQ,YAAY,MAAM,CAAC,IAAI;AAC/B,qBAAO;AAAA,YACX;AAAA,UACJ,WACS,cAAc,UAAU,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,KAAK;AACzE,gBAAI,QAAQ,QAAQ,MAAM,CAAC,EAAE,OAAO,IAAI,KAAK,IAAI;AAC7C,sBAAQ,YAAY,MAAM,CAAC,IAAI;AAC/B,sBAAQ,YAAY,MAAM,CAAC,IAAI;AAC/B,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,cAAI,cAAc,MAAM,aAAa,QAAQ;AACzC,oBAAQ,YAAY,MAAM,CAAC,IAAI;AAAA,UACnC,WACS,cAAc,OAAO,cAAc,UAAU,qBAAqB;AACvE,oBAAQ,YAAY,MAAM,CAAC,IAAI;AAAA,UACnC,OACK;AACD,oBAAQ,YAAY,MAAM,CAAC,IAAI;AAAA,UACnC;AACA,cAAI,SAAS;AACT,mBAAO;AAAA;AAEP,mBAAO;AAAA,QACf;AAAA,MACJ,GAAG,KAAK,SAAS,SAAS;AAAA,IAE1B,CAAC;AAED,QAAI,OAAO,iBAAgB,CAAC,WAAU,WAAU,UAAS,eAAc,iBAAgB,iCAAgC,mCAAkC,yBAAwB,0BAA0B,GAAG,SAASD,UAASC,UAASC,SAAO;AAAC;AACjP,UAAI,MAAMF,SAAQ,YAAY;AAC9B,UAAI,WAAWA,SAAQ,QAAQ,EAAE;AACjC,UAAI,qBAAqBA,SAAQ,wBAAwB,EAAE;AAC3D,UAAI,uBAAuBA,SAAQ,0BAA0B,EAAE;AAC/D,UAAI,WAAWA,SAAQ,gBAAgB,EAAE;AACzC,UAAI,eAAeA,SAAQ,yBAAyB,EAAE;AACtD,UAAI,OAAO,WAAY;AACnB,aAAK,iBAAiB;AACtB,aAAK,WAAW,IAAI,qBAAqB;AACzC,aAAK,eAAe,IAAI,SAAS;AACjC,aAAK,aAAa,KAAK;AAAA,MAC3B;AACA,UAAI,SAAS,MAAM,QAAQ;AAC3B,OAAC,WAAY;AACT,aAAK,mBAAmB,CAAC,GAAG;AAC5B,aAAK,oBAAoB,SAAU,OAAO,MAAM,KAAK;AACjD,cAAI,SAAS,KAAK,WAAW,IAAI;AACjC,cAAI,SAAS,SAAS;AAClB,gBAAI,QAAQ,KAAK,MAAM,iBAAiB;AACxC,gBAAI,OAAO;AACP,wBAAU;AAAA,YACd;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AACA,aAAK,eAAe,SAAU,OAAO,MAAM,OAAO;AAC9C,iBAAO,KAAK,SAAS,aAAa,MAAM,KAAK;AAAA,QACjD;AACA,aAAK,cAAc,SAAU,OAAO,KAAK,KAAK;AAC1C,eAAK,SAAS,YAAY,KAAK,GAAG;AAAA,QACtC;AACA,aAAK,eAAe,SAAU,SAAS;AACnC,cAAI,SAAS,IAAI,aAAa,CAAC,KAAK,GAAG,wBAAwB,YAAY;AAC3E,iBAAO,iBAAiB,QAAQ,YAAY,CAAC;AAC7C,iBAAO,GAAG,YAAY,SAAU,SAAS;AACrC,oBAAQ,eAAe,QAAQ,IAAI;AAAA,UACvC,CAAC;AACD,iBAAO,GAAG,aAAa,WAAY;AAC/B,oBAAQ,iBAAiB;AAAA,UAC7B,CAAC;AACD,iBAAO;AAAA,QACX;AACA,aAAK,MAAM;AAAA,MACf,GAAG,KAAK,KAAK,SAAS;AACtB,MAAAC,SAAQ,OAAO;AAAA,IAEf,CAAC;AAAkB,KAAC,WAAW;AACX,UAAI,QAAQ,CAAC,eAAe,GAAG,SAAS,GAAG;AACvC,YAAI,OAAO,UAAU,YAAY,OAAO,WAAW,YAAY,QAAQ;AACnE,iBAAO,UAAU;AAAA,QACrB;AAAA,MACJ,CAAC;AAAA,IACL,GAAG;AAAA;AAAA;", "names": ["require", "exports", "module"]}