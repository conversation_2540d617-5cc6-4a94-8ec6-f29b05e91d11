declare module 'ace-builds/webpack-resolver';
declare module 'ace-builds/esm-resolver';
declare module 'ace-builds/src-noconflict/ace';
declare module 'ace-builds/src-noconflict/ext-beautify';
declare module 'ace-builds/src-noconflict/ext-code_lens';
declare module 'ace-builds/src-noconflict/ext-command_bar';
declare module 'ace-builds/src-noconflict/ext-elastic_tabstops_lite';
declare module 'ace-builds/src-noconflict/ext-emmet';
declare module 'ace-builds/src-noconflict/ext-error_marker';
declare module 'ace-builds/src-noconflict/ext-hardwrap';
declare module 'ace-builds/src-noconflict/ext-inline_autocomplete';
declare module 'ace-builds/src-noconflict/ext-keybinding_menu';
declare module 'ace-builds/src-noconflict/ext-language_tools';
declare module 'ace-builds/src-noconflict/ext-linking';
declare module 'ace-builds/src-noconflict/ext-modelist';
declare module 'ace-builds/src-noconflict/ext-options';
declare module 'ace-builds/src-noconflict/ext-prompt';
declare module 'ace-builds/src-noconflict/ext-rtl';
declare module 'ace-builds/src-noconflict/ext-searchbox';
declare module 'ace-builds/src-noconflict/ext-settings_menu';
declare module 'ace-builds/src-noconflict/ext-simple_tokenizer';
declare module 'ace-builds/src-noconflict/ext-spellcheck';
declare module 'ace-builds/src-noconflict/ext-split';
declare module 'ace-builds/src-noconflict/ext-static_highlight';
declare module 'ace-builds/src-noconflict/ext-statusbar';
declare module 'ace-builds/src-noconflict/ext-textarea';
declare module 'ace-builds/src-noconflict/ext-themelist';
declare module 'ace-builds/src-noconflict/ext-whitespace';
declare module 'ace-builds/src-noconflict/keybinding-emacs';
declare module 'ace-builds/src-noconflict/keybinding-sublime';
declare module 'ace-builds/src-noconflict/keybinding-vim';
declare module 'ace-builds/src-noconflict/keybinding-vscode';
declare module 'ace-builds/src-noconflict/mode-abap';
declare module 'ace-builds/src-noconflict/mode-abc';
declare module 'ace-builds/src-noconflict/mode-actionscript';
declare module 'ace-builds/src-noconflict/mode-ada';
declare module 'ace-builds/src-noconflict/mode-alda';
declare module 'ace-builds/src-noconflict/mode-apache_conf';
declare module 'ace-builds/src-noconflict/mode-apex';
declare module 'ace-builds/src-noconflict/mode-applescript';
declare module 'ace-builds/src-noconflict/mode-aql';
declare module 'ace-builds/src-noconflict/mode-asciidoc';
declare module 'ace-builds/src-noconflict/mode-asl';
declare module 'ace-builds/src-noconflict/mode-assembly_arm32';
declare module 'ace-builds/src-noconflict/mode-assembly_x86';
declare module 'ace-builds/src-noconflict/mode-astro';
declare module 'ace-builds/src-noconflict/mode-autohotkey';
declare module 'ace-builds/src-noconflict/mode-basic';
declare module 'ace-builds/src-noconflict/mode-batchfile';
declare module 'ace-builds/src-noconflict/mode-bibtex';
declare module 'ace-builds/src-noconflict/mode-c9search';
declare module 'ace-builds/src-noconflict/mode-c_cpp';
declare module 'ace-builds/src-noconflict/mode-cirru';
declare module 'ace-builds/src-noconflict/mode-clojure';
declare module 'ace-builds/src-noconflict/mode-cobol';
declare module 'ace-builds/src-noconflict/mode-coffee';
declare module 'ace-builds/src-noconflict/mode-coldfusion';
declare module 'ace-builds/src-noconflict/mode-crystal';
declare module 'ace-builds/src-noconflict/mode-csharp';
declare module 'ace-builds/src-noconflict/mode-csound_document';
declare module 'ace-builds/src-noconflict/mode-csound_orchestra';
declare module 'ace-builds/src-noconflict/mode-csound_score';
declare module 'ace-builds/src-noconflict/mode-csp';
declare module 'ace-builds/src-noconflict/mode-css';
declare module 'ace-builds/src-noconflict/mode-curly';
declare module 'ace-builds/src-noconflict/mode-cuttlefish';
declare module 'ace-builds/src-noconflict/mode-d';
declare module 'ace-builds/src-noconflict/mode-dart';
declare module 'ace-builds/src-noconflict/mode-diff';
declare module 'ace-builds/src-noconflict/mode-django';
declare module 'ace-builds/src-noconflict/mode-dockerfile';
declare module 'ace-builds/src-noconflict/mode-dot';
declare module 'ace-builds/src-noconflict/mode-drools';
declare module 'ace-builds/src-noconflict/mode-edifact';
declare module 'ace-builds/src-noconflict/mode-eiffel';
declare module 'ace-builds/src-noconflict/mode-ejs';
declare module 'ace-builds/src-noconflict/mode-elixir';
declare module 'ace-builds/src-noconflict/mode-elm';
declare module 'ace-builds/src-noconflict/mode-erlang';
declare module 'ace-builds/src-noconflict/mode-flix';
declare module 'ace-builds/src-noconflict/mode-forth';
declare module 'ace-builds/src-noconflict/mode-fortran';
declare module 'ace-builds/src-noconflict/mode-fsharp';
declare module 'ace-builds/src-noconflict/mode-fsl';
declare module 'ace-builds/src-noconflict/mode-ftl';
declare module 'ace-builds/src-noconflict/mode-gcode';
declare module 'ace-builds/src-noconflict/mode-gherkin';
declare module 'ace-builds/src-noconflict/mode-gitignore';
declare module 'ace-builds/src-noconflict/mode-glsl';
declare module 'ace-builds/src-noconflict/mode-gobstones';
declare module 'ace-builds/src-noconflict/mode-golang';
declare module 'ace-builds/src-noconflict/mode-graphqlschema';
declare module 'ace-builds/src-noconflict/mode-groovy';
declare module 'ace-builds/src-noconflict/mode-haml';
declare module 'ace-builds/src-noconflict/mode-handlebars';
declare module 'ace-builds/src-noconflict/mode-haskell';
declare module 'ace-builds/src-noconflict/mode-haskell_cabal';
declare module 'ace-builds/src-noconflict/mode-haxe';
declare module 'ace-builds/src-noconflict/mode-hjson';
declare module 'ace-builds/src-noconflict/mode-html';
declare module 'ace-builds/src-noconflict/mode-html_elixir';
declare module 'ace-builds/src-noconflict/mode-html_ruby';
declare module 'ace-builds/src-noconflict/mode-ini';
declare module 'ace-builds/src-noconflict/mode-io';
declare module 'ace-builds/src-noconflict/mode-ion';
declare module 'ace-builds/src-noconflict/mode-jack';
declare module 'ace-builds/src-noconflict/mode-jade';
declare module 'ace-builds/src-noconflict/mode-java';
declare module 'ace-builds/src-noconflict/mode-javascript';
declare module 'ace-builds/src-noconflict/mode-jexl';
declare module 'ace-builds/src-noconflict/mode-json';
declare module 'ace-builds/src-noconflict/mode-json5';
declare module 'ace-builds/src-noconflict/mode-jsoniq';
declare module 'ace-builds/src-noconflict/mode-jsp';
declare module 'ace-builds/src-noconflict/mode-jssm';
declare module 'ace-builds/src-noconflict/mode-jsx';
declare module 'ace-builds/src-noconflict/mode-julia';
declare module 'ace-builds/src-noconflict/mode-kotlin';
declare module 'ace-builds/src-noconflict/mode-latex';
declare module 'ace-builds/src-noconflict/mode-latte';
declare module 'ace-builds/src-noconflict/mode-less';
declare module 'ace-builds/src-noconflict/mode-liquid';
declare module 'ace-builds/src-noconflict/mode-lisp';
declare module 'ace-builds/src-noconflict/mode-livescript';
declare module 'ace-builds/src-noconflict/mode-logiql';
declare module 'ace-builds/src-noconflict/mode-logtalk';
declare module 'ace-builds/src-noconflict/mode-lsl';
declare module 'ace-builds/src-noconflict/mode-lua';
declare module 'ace-builds/src-noconflict/mode-luapage';
declare module 'ace-builds/src-noconflict/mode-lucene';
declare module 'ace-builds/src-noconflict/mode-makefile';
declare module 'ace-builds/src-noconflict/mode-markdown';
declare module 'ace-builds/src-noconflict/mode-mask';
declare module 'ace-builds/src-noconflict/mode-matlab';
declare module 'ace-builds/src-noconflict/mode-maze';
declare module 'ace-builds/src-noconflict/mode-mediawiki';
declare module 'ace-builds/src-noconflict/mode-mel';
declare module 'ace-builds/src-noconflict/mode-mips';
declare module 'ace-builds/src-noconflict/mode-mixal';
declare module 'ace-builds/src-noconflict/mode-mushcode';
declare module 'ace-builds/src-noconflict/mode-mysql';
declare module 'ace-builds/src-noconflict/mode-nasal';
declare module 'ace-builds/src-noconflict/mode-nginx';
declare module 'ace-builds/src-noconflict/mode-nim';
declare module 'ace-builds/src-noconflict/mode-nix';
declare module 'ace-builds/src-noconflict/mode-nsis';
declare module 'ace-builds/src-noconflict/mode-nunjucks';
declare module 'ace-builds/src-noconflict/mode-objectivec';
declare module 'ace-builds/src-noconflict/mode-ocaml';
declare module 'ace-builds/src-noconflict/mode-odin';
declare module 'ace-builds/src-noconflict/mode-partiql';
declare module 'ace-builds/src-noconflict/mode-pascal';
declare module 'ace-builds/src-noconflict/mode-perl';
declare module 'ace-builds/src-noconflict/mode-pgsql';
declare module 'ace-builds/src-noconflict/mode-php';
declare module 'ace-builds/src-noconflict/mode-php_laravel_blade';
declare module 'ace-builds/src-noconflict/mode-pig';
declare module 'ace-builds/src-noconflict/mode-plain_text';
declare module 'ace-builds/src-noconflict/mode-plsql';
declare module 'ace-builds/src-noconflict/mode-powershell';
declare module 'ace-builds/src-noconflict/mode-praat';
declare module 'ace-builds/src-noconflict/mode-prisma';
declare module 'ace-builds/src-noconflict/mode-prolog';
declare module 'ace-builds/src-noconflict/mode-properties';
declare module 'ace-builds/src-noconflict/mode-protobuf';
declare module 'ace-builds/src-noconflict/mode-prql';
declare module 'ace-builds/src-noconflict/mode-puppet';
declare module 'ace-builds/src-noconflict/mode-python';
declare module 'ace-builds/src-noconflict/mode-qml';
declare module 'ace-builds/src-noconflict/mode-r';
declare module 'ace-builds/src-noconflict/mode-raku';
declare module 'ace-builds/src-noconflict/mode-razor';
declare module 'ace-builds/src-noconflict/mode-rdoc';
declare module 'ace-builds/src-noconflict/mode-red';
declare module 'ace-builds/src-noconflict/mode-redshift';
declare module 'ace-builds/src-noconflict/mode-rhtml';
declare module 'ace-builds/src-noconflict/mode-robot';
declare module 'ace-builds/src-noconflict/mode-rst';
declare module 'ace-builds/src-noconflict/mode-ruby';
declare module 'ace-builds/src-noconflict/mode-rust';
declare module 'ace-builds/src-noconflict/mode-sac';
declare module 'ace-builds/src-noconflict/mode-sass';
declare module 'ace-builds/src-noconflict/mode-scad';
declare module 'ace-builds/src-noconflict/mode-scala';
declare module 'ace-builds/src-noconflict/mode-scheme';
declare module 'ace-builds/src-noconflict/mode-scrypt';
declare module 'ace-builds/src-noconflict/mode-scss';
declare module 'ace-builds/src-noconflict/mode-sh';
declare module 'ace-builds/src-noconflict/mode-sjs';
declare module 'ace-builds/src-noconflict/mode-slim';
declare module 'ace-builds/src-noconflict/mode-smarty';
declare module 'ace-builds/src-noconflict/mode-smithy';
declare module 'ace-builds/src-noconflict/mode-snippets';
declare module 'ace-builds/src-noconflict/mode-soy_template';
declare module 'ace-builds/src-noconflict/mode-space';
declare module 'ace-builds/src-noconflict/mode-sparql';
declare module 'ace-builds/src-noconflict/mode-sql';
declare module 'ace-builds/src-noconflict/mode-sqlserver';
declare module 'ace-builds/src-noconflict/mode-stylus';
declare module 'ace-builds/src-noconflict/mode-svg';
declare module 'ace-builds/src-noconflict/mode-swift';
declare module 'ace-builds/src-noconflict/mode-tcl';
declare module 'ace-builds/src-noconflict/mode-terraform';
declare module 'ace-builds/src-noconflict/mode-tex';
declare module 'ace-builds/src-noconflict/mode-text';
declare module 'ace-builds/src-noconflict/mode-textile';
declare module 'ace-builds/src-noconflict/mode-toml';
declare module 'ace-builds/src-noconflict/mode-tsx';
declare module 'ace-builds/src-noconflict/mode-turtle';
declare module 'ace-builds/src-noconflict/mode-twig';
declare module 'ace-builds/src-noconflict/mode-typescript';
declare module 'ace-builds/src-noconflict/mode-vala';
declare module 'ace-builds/src-noconflict/mode-vbscript';
declare module 'ace-builds/src-noconflict/mode-velocity';
declare module 'ace-builds/src-noconflict/mode-verilog';
declare module 'ace-builds/src-noconflict/mode-vhdl';
declare module 'ace-builds/src-noconflict/mode-visualforce';
declare module 'ace-builds/src-noconflict/mode-vue';
declare module 'ace-builds/src-noconflict/mode-wollok';
declare module 'ace-builds/src-noconflict/mode-xml';
declare module 'ace-builds/src-noconflict/mode-xquery';
declare module 'ace-builds/src-noconflict/mode-yaml';
declare module 'ace-builds/src-noconflict/mode-zeek';
declare module 'ace-builds/src-noconflict/mode-zig';
declare module 'ace-builds/src-noconflict/theme-ambiance';
declare module 'ace-builds/src-noconflict/theme-chaos';
declare module 'ace-builds/src-noconflict/theme-chrome';
declare module 'ace-builds/src-noconflict/theme-cloud9_day';
declare module 'ace-builds/src-noconflict/theme-cloud9_night';
declare module 'ace-builds/src-noconflict/theme-cloud9_night_low_color';
declare module 'ace-builds/src-noconflict/theme-cloud_editor';
declare module 'ace-builds/src-noconflict/theme-cloud_editor_dark';
declare module 'ace-builds/src-noconflict/theme-clouds';
declare module 'ace-builds/src-noconflict/theme-clouds_midnight';
declare module 'ace-builds/src-noconflict/theme-cobalt';
declare module 'ace-builds/src-noconflict/theme-crimson_editor';
declare module 'ace-builds/src-noconflict/theme-dawn';
declare module 'ace-builds/src-noconflict/theme-dracula';
declare module 'ace-builds/src-noconflict/theme-dreamweaver';
declare module 'ace-builds/src-noconflict/theme-eclipse';
declare module 'ace-builds/src-noconflict/theme-github';
declare module 'ace-builds/src-noconflict/theme-github_dark';
declare module 'ace-builds/src-noconflict/theme-github_light_default';
declare module 'ace-builds/src-noconflict/theme-gob';
declare module 'ace-builds/src-noconflict/theme-gruvbox';
declare module 'ace-builds/src-noconflict/theme-gruvbox_dark_hard';
declare module 'ace-builds/src-noconflict/theme-gruvbox_light_hard';
declare module 'ace-builds/src-noconflict/theme-idle_fingers';
declare module 'ace-builds/src-noconflict/theme-iplastic';
declare module 'ace-builds/src-noconflict/theme-katzenmilch';
declare module 'ace-builds/src-noconflict/theme-kr_theme';
declare module 'ace-builds/src-noconflict/theme-kuroir';
declare module 'ace-builds/src-noconflict/theme-merbivore';
declare module 'ace-builds/src-noconflict/theme-merbivore_soft';
declare module 'ace-builds/src-noconflict/theme-mono_industrial';
declare module 'ace-builds/src-noconflict/theme-monokai';
declare module 'ace-builds/src-noconflict/theme-nord_dark';
declare module 'ace-builds/src-noconflict/theme-one_dark';
declare module 'ace-builds/src-noconflict/theme-pastel_on_dark';
declare module 'ace-builds/src-noconflict/theme-solarized_dark';
declare module 'ace-builds/src-noconflict/theme-solarized_light';
declare module 'ace-builds/src-noconflict/theme-sqlserver';
declare module 'ace-builds/src-noconflict/theme-terminal';
declare module 'ace-builds/src-noconflict/theme-textmate';
declare module 'ace-builds/src-noconflict/theme-tomorrow';
declare module 'ace-builds/src-noconflict/theme-tomorrow_night';
declare module 'ace-builds/src-noconflict/theme-tomorrow_night_blue';
declare module 'ace-builds/src-noconflict/theme-tomorrow_night_bright';
declare module 'ace-builds/src-noconflict/theme-tomorrow_night_eighties';
declare module 'ace-builds/src-noconflict/theme-twilight';
declare module 'ace-builds/src-noconflict/theme-vibrant_ink';
declare module 'ace-builds/src-noconflict/theme-xcode';
declare module 'ace-builds/src-noconflict/snippets/abap';
declare module 'ace-builds/src-noconflict/snippets/abc';
declare module 'ace-builds/src-noconflict/snippets/actionscript';
declare module 'ace-builds/src-noconflict/snippets/ada';
declare module 'ace-builds/src-noconflict/snippets/alda';
declare module 'ace-builds/src-noconflict/snippets/apache_conf';
declare module 'ace-builds/src-noconflict/snippets/apex';
declare module 'ace-builds/src-noconflict/snippets/applescript';
declare module 'ace-builds/src-noconflict/snippets/aql';
declare module 'ace-builds/src-noconflict/snippets/asciidoc';
declare module 'ace-builds/src-noconflict/snippets/asl';
declare module 'ace-builds/src-noconflict/snippets/assembly_arm32';
declare module 'ace-builds/src-noconflict/snippets/assembly_x86';
declare module 'ace-builds/src-noconflict/snippets/astro';
declare module 'ace-builds/src-noconflict/snippets/autohotkey';
declare module 'ace-builds/src-noconflict/snippets/basic';
declare module 'ace-builds/src-noconflict/snippets/batchfile';
declare module 'ace-builds/src-noconflict/snippets/bibtex';
declare module 'ace-builds/src-noconflict/snippets/c9search';
declare module 'ace-builds/src-noconflict/snippets/c_cpp';
declare module 'ace-builds/src-noconflict/snippets/cirru';
declare module 'ace-builds/src-noconflict/snippets/clojure';
declare module 'ace-builds/src-noconflict/snippets/cobol';
declare module 'ace-builds/src-noconflict/snippets/coffee';
declare module 'ace-builds/src-noconflict/snippets/coldfusion';
declare module 'ace-builds/src-noconflict/snippets/crystal';
declare module 'ace-builds/src-noconflict/snippets/csharp';
declare module 'ace-builds/src-noconflict/snippets/csound_document';
declare module 'ace-builds/src-noconflict/snippets/csound_orchestra';
declare module 'ace-builds/src-noconflict/snippets/csound_score';
declare module 'ace-builds/src-noconflict/snippets/csp';
declare module 'ace-builds/src-noconflict/snippets/css';
declare module 'ace-builds/src-noconflict/snippets/curly';
declare module 'ace-builds/src-noconflict/snippets/cuttlefish';
declare module 'ace-builds/src-noconflict/snippets/d';
declare module 'ace-builds/src-noconflict/snippets/dart';
declare module 'ace-builds/src-noconflict/snippets/diff';
declare module 'ace-builds/src-noconflict/snippets/django';
declare module 'ace-builds/src-noconflict/snippets/dockerfile';
declare module 'ace-builds/src-noconflict/snippets/dot';
declare module 'ace-builds/src-noconflict/snippets/drools';
declare module 'ace-builds/src-noconflict/snippets/edifact';
declare module 'ace-builds/src-noconflict/snippets/eiffel';
declare module 'ace-builds/src-noconflict/snippets/ejs';
declare module 'ace-builds/src-noconflict/snippets/elixir';
declare module 'ace-builds/src-noconflict/snippets/elm';
declare module 'ace-builds/src-noconflict/snippets/erlang';
declare module 'ace-builds/src-noconflict/snippets/flix';
declare module 'ace-builds/src-noconflict/snippets/forth';
declare module 'ace-builds/src-noconflict/snippets/fortran';
declare module 'ace-builds/src-noconflict/snippets/fsharp';
declare module 'ace-builds/src-noconflict/snippets/fsl';
declare module 'ace-builds/src-noconflict/snippets/ftl';
declare module 'ace-builds/src-noconflict/snippets/gcode';
declare module 'ace-builds/src-noconflict/snippets/gherkin';
declare module 'ace-builds/src-noconflict/snippets/gitignore';
declare module 'ace-builds/src-noconflict/snippets/glsl';
declare module 'ace-builds/src-noconflict/snippets/gobstones';
declare module 'ace-builds/src-noconflict/snippets/golang';
declare module 'ace-builds/src-noconflict/snippets/graphqlschema';
declare module 'ace-builds/src-noconflict/snippets/groovy';
declare module 'ace-builds/src-noconflict/snippets/haml';
declare module 'ace-builds/src-noconflict/snippets/handlebars';
declare module 'ace-builds/src-noconflict/snippets/haskell';
declare module 'ace-builds/src-noconflict/snippets/haskell_cabal';
declare module 'ace-builds/src-noconflict/snippets/haxe';
declare module 'ace-builds/src-noconflict/snippets/hjson';
declare module 'ace-builds/src-noconflict/snippets/html';
declare module 'ace-builds/src-noconflict/snippets/html_elixir';
declare module 'ace-builds/src-noconflict/snippets/html_ruby';
declare module 'ace-builds/src-noconflict/snippets/ini';
declare module 'ace-builds/src-noconflict/snippets/io';
declare module 'ace-builds/src-noconflict/snippets/ion';
declare module 'ace-builds/src-noconflict/snippets/jack';
declare module 'ace-builds/src-noconflict/snippets/jade';
declare module 'ace-builds/src-noconflict/snippets/java';
declare module 'ace-builds/src-noconflict/snippets/javascript';
declare module 'ace-builds/src-noconflict/snippets/jexl';
declare module 'ace-builds/src-noconflict/snippets/json';
declare module 'ace-builds/src-noconflict/snippets/json5';
declare module 'ace-builds/src-noconflict/snippets/jsoniq';
declare module 'ace-builds/src-noconflict/snippets/jsp';
declare module 'ace-builds/src-noconflict/snippets/jssm';
declare module 'ace-builds/src-noconflict/snippets/jsx';
declare module 'ace-builds/src-noconflict/snippets/julia';
declare module 'ace-builds/src-noconflict/snippets/kotlin';
declare module 'ace-builds/src-noconflict/snippets/latex';
declare module 'ace-builds/src-noconflict/snippets/latte';
declare module 'ace-builds/src-noconflict/snippets/less';
declare module 'ace-builds/src-noconflict/snippets/liquid';
declare module 'ace-builds/src-noconflict/snippets/lisp';
declare module 'ace-builds/src-noconflict/snippets/livescript';
declare module 'ace-builds/src-noconflict/snippets/logiql';
declare module 'ace-builds/src-noconflict/snippets/logtalk';
declare module 'ace-builds/src-noconflict/snippets/lsl';
declare module 'ace-builds/src-noconflict/snippets/lua';
declare module 'ace-builds/src-noconflict/snippets/luapage';
declare module 'ace-builds/src-noconflict/snippets/lucene';
declare module 'ace-builds/src-noconflict/snippets/makefile';
declare module 'ace-builds/src-noconflict/snippets/markdown';
declare module 'ace-builds/src-noconflict/snippets/mask';
declare module 'ace-builds/src-noconflict/snippets/matlab';
declare module 'ace-builds/src-noconflict/snippets/maze';
declare module 'ace-builds/src-noconflict/snippets/mediawiki';
declare module 'ace-builds/src-noconflict/snippets/mel';
declare module 'ace-builds/src-noconflict/snippets/mips';
declare module 'ace-builds/src-noconflict/snippets/mixal';
declare module 'ace-builds/src-noconflict/snippets/mushcode';
declare module 'ace-builds/src-noconflict/snippets/mysql';
declare module 'ace-builds/src-noconflict/snippets/nasal';
declare module 'ace-builds/src-noconflict/snippets/nginx';
declare module 'ace-builds/src-noconflict/snippets/nim';
declare module 'ace-builds/src-noconflict/snippets/nix';
declare module 'ace-builds/src-noconflict/snippets/nsis';
declare module 'ace-builds/src-noconflict/snippets/nunjucks';
declare module 'ace-builds/src-noconflict/snippets/objectivec';
declare module 'ace-builds/src-noconflict/snippets/ocaml';
declare module 'ace-builds/src-noconflict/snippets/odin';
declare module 'ace-builds/src-noconflict/snippets/partiql';
declare module 'ace-builds/src-noconflict/snippets/pascal';
declare module 'ace-builds/src-noconflict/snippets/perl';
declare module 'ace-builds/src-noconflict/snippets/pgsql';
declare module 'ace-builds/src-noconflict/snippets/php';
declare module 'ace-builds/src-noconflict/snippets/php_laravel_blade';
declare module 'ace-builds/src-noconflict/snippets/pig';
declare module 'ace-builds/src-noconflict/snippets/plain_text';
declare module 'ace-builds/src-noconflict/snippets/plsql';
declare module 'ace-builds/src-noconflict/snippets/powershell';
declare module 'ace-builds/src-noconflict/snippets/praat';
declare module 'ace-builds/src-noconflict/snippets/prisma';
declare module 'ace-builds/src-noconflict/snippets/prolog';
declare module 'ace-builds/src-noconflict/snippets/properties';
declare module 'ace-builds/src-noconflict/snippets/protobuf';
declare module 'ace-builds/src-noconflict/snippets/prql';
declare module 'ace-builds/src-noconflict/snippets/puppet';
declare module 'ace-builds/src-noconflict/snippets/python';
declare module 'ace-builds/src-noconflict/snippets/qml';
declare module 'ace-builds/src-noconflict/snippets/r';
declare module 'ace-builds/src-noconflict/snippets/raku';
declare module 'ace-builds/src-noconflict/snippets/razor';
declare module 'ace-builds/src-noconflict/snippets/rdoc';
declare module 'ace-builds/src-noconflict/snippets/red';
declare module 'ace-builds/src-noconflict/snippets/redshift';
declare module 'ace-builds/src-noconflict/snippets/rhtml';
declare module 'ace-builds/src-noconflict/snippets/robot';
declare module 'ace-builds/src-noconflict/snippets/rst';
declare module 'ace-builds/src-noconflict/snippets/ruby';
declare module 'ace-builds/src-noconflict/snippets/rust';
declare module 'ace-builds/src-noconflict/snippets/sac';
declare module 'ace-builds/src-noconflict/snippets/sass';
declare module 'ace-builds/src-noconflict/snippets/scad';
declare module 'ace-builds/src-noconflict/snippets/scala';
declare module 'ace-builds/src-noconflict/snippets/scheme';
declare module 'ace-builds/src-noconflict/snippets/scrypt';
declare module 'ace-builds/src-noconflict/snippets/scss';
declare module 'ace-builds/src-noconflict/snippets/sh';
declare module 'ace-builds/src-noconflict/snippets/sjs';
declare module 'ace-builds/src-noconflict/snippets/slim';
declare module 'ace-builds/src-noconflict/snippets/smarty';
declare module 'ace-builds/src-noconflict/snippets/smithy';
declare module 'ace-builds/src-noconflict/snippets/snippets';
declare module 'ace-builds/src-noconflict/snippets/soy_template';
declare module 'ace-builds/src-noconflict/snippets/space';
declare module 'ace-builds/src-noconflict/snippets/sparql';
declare module 'ace-builds/src-noconflict/snippets/sql';
declare module 'ace-builds/src-noconflict/snippets/sqlserver';
declare module 'ace-builds/src-noconflict/snippets/stylus';
declare module 'ace-builds/src-noconflict/snippets/svg';
declare module 'ace-builds/src-noconflict/snippets/swift';
declare module 'ace-builds/src-noconflict/snippets/tcl';
declare module 'ace-builds/src-noconflict/snippets/terraform';
declare module 'ace-builds/src-noconflict/snippets/tex';
declare module 'ace-builds/src-noconflict/snippets/text';
declare module 'ace-builds/src-noconflict/snippets/textile';
declare module 'ace-builds/src-noconflict/snippets/toml';
declare module 'ace-builds/src-noconflict/snippets/tsx';
declare module 'ace-builds/src-noconflict/snippets/turtle';
declare module 'ace-builds/src-noconflict/snippets/twig';
declare module 'ace-builds/src-noconflict/snippets/typescript';
declare module 'ace-builds/src-noconflict/snippets/vala';
declare module 'ace-builds/src-noconflict/snippets/vbscript';
declare module 'ace-builds/src-noconflict/snippets/velocity';
declare module 'ace-builds/src-noconflict/snippets/verilog';
declare module 'ace-builds/src-noconflict/snippets/vhdl';
declare module 'ace-builds/src-noconflict/snippets/visualforce';
declare module 'ace-builds/src-noconflict/snippets/vue';
declare module 'ace-builds/src-noconflict/snippets/wollok';
declare module 'ace-builds/src-noconflict/snippets/xml';
declare module 'ace-builds/src-noconflict/snippets/xquery';
declare module 'ace-builds/src-noconflict/snippets/yaml';
declare module 'ace-builds/src-noconflict/snippets/zeek';
declare module 'ace-builds/src-noconflict/snippets/zig';
