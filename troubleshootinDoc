Multi-line inputs
When writing script actions, you may want to use multiple lines. The full compound action gets parsed as YAML, so make sure you add a vertical bar and then write your code normally.


script:
  code: |
    a = 1
    b = 2
    a + b

DSL Escaping
In our Data Mapper language, every input gets parsed as a DSL string. So numeric and boolean constants need to be quoted as follows


integer_value: '2'
decimal_value: '12.34'
boolean_value: 'true'

Additionally, because we use YAML as the authoring language we have to write constants to circumvent built-in yaml syntax. This means you need an extra set of quotes surrounding string/list/object constants


string_value: '"abc"'
list_value: '[1, 2, 3, "abc"}'
object_value: '{"a": "b", "c": 123}'

If you don't like managing those quotes yaml also makes it pretty easy to do this outer wrapping using the block quote syntax


string_value: >
  "abc"
list_value: >
  [1, 2, 3, "abc"]
object_value: >
  {"a": "b", "c": 123}


Missing fields from an HTTP Action
If you have fields from an HTTP Action response that are missing in when the action is referenced in the compound action or from the bot's response.

This may occur due to the action filtering values based on it's action schema. To resolve this, ensure that all of the fields from the actions response are referenced in the output schema. To do this:

Go to Agent Studio, select Plugins in the top bar, and then select actions.



Select the action that is causing the missing fields.



Select the "Response" tab



Ensure that each response field from the API Response (left-hand side) is mentioned in the Output Schema (right-hand side). If you are unsure if all the fields are covered, you can select "Generate From Response" and we will generate a schema that includes every field from the API response.

FYI: If you see the following text at the bottom of the response page, it means that Moveworks has detected a difference between fields in the API Response and in the Output Schema.