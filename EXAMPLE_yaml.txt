steps:
  - action:
      action_name: ms_graph_GET_user_mfa_methods
      input_args:
        graph_id_or_upn: meta_info.user.external_system_identities.ms_graph.external_id
      output_key: ms_graph_GET_user_mfa_methods_result
  - for:
      each: mfa
      steps:
        - switch:
            cases:
              - steps:
                  - action:
                      action_name: ms_graph_DELETE_user_phone_mfa_method
                      input_args:
                        phoneMethodId: mfa.id
                        id_or_upn: meta_info.user.external_system_identities.ms_graph.external_id
                      output_key: ms_graph_DELETE_user_phone_mfa_method_result
                  - return:
                      output_mapper:
                        instructions_for_model: >
                          "Phone method for the phone_number_deleted has been
                          successfully removed from MFA"
                        phone_number_deleted: mfa.phoneNumber
                condition: mfa["@odata.type"] == "#microsoft.graph.phoneAuthenticationMethod"
            default:
              steps:
                - return:
                    output_mapper: {}
      index: mfa_index
      in: data.ms_graph_GET_user_mfa_methods_result.value
      output_key: mfa_delete_response
  - for:
      each: mfa
      steps:
        - switch:
            cases:
              - steps:
                  - action:
                      action_name: ms_graph_DELETE_user_ms_mfa_method
                      input_args:
                        method_id: mfa.id
                        id_or_upn: meta_info.user.external_system_identities.ms_graph.external_id
                      output_key: ms_graph_DELETE_user_ms_mfa_method_result
                  - return:
                      output_mapper:
                        instructions_for_model: >
                          "Microsoft MFA method for the device_tag
                          display_name_of_device has been successfully removed
                          from MFA"
                        device_tag: mfa.deviceTag
                        display_name_of_device: mfa.displayName
                condition: mfa["@odata.type"] ==
                  "#microsoft.graph.microsoftAuthenticatorAuthenticationMethod"
            default:
              steps:
                - return:
                    output_mapper: {}
      index: mfa_index
      in: data.ms_graph_GET_user_mfa_methods_result.value
      output_key: ms_mfa_delete_response
  - action:
      action_name: JIra_servicedesk_create_request
      input_args:
        requested_for: meta_info.user.external_system_identities.jira_service_desk.external_id
        description:
          RENDER():
            template: >
              {{user}} has requested their MFA methods for MS GRAPH be reset via
              Nina and this was successfully completed
            args:
              user: meta_info.user.email_addr
        request_id: '"45"'
        service_desk_id: '"3"'
        summary: '"MFA Reset Flow via Nina"'
      output_key: JIra_servicedesk_create_request_result
  - action:
      action_name: JIRA_set_svc_acct_as_assignee_on_ticket
      input_args:
        issueIdOrKey: data.JIra_servicedesk_create_request_result.issueKey
      output_key: JIRA_set_svc_acct_as_assignee_on_ticket_result
  - action:
      action_name: JIRA_resolve_ten_zero_four_zero_three_ticket
      input_args:
        issueIdOrKey: data.JIra_servicedesk_create_request_result.issueKey
      output_key: JIRA_resolve_ten_zero_four_zero_three_ticket_result
  - return:
      output_mapper:
        jira_ticket:
          RENDER():
            template: >
              A JIRA ticket has been created and closed for tracking purposes at
              {{link}} with the ID {{ticket_number}}
            args:
              ticket_number: data.JIra_servicedesk_create_request_result.issueKey
              link: data["JIra_servicedesk_create_request_result"]["_links"]["web"]
        message_user: >
          "Give the user details on the MFA deleted from
          output_of_mfa_phone_deletions and output_of_ms_mfa_deletions and
          Inform the user to go to https://myaccount.microsoft.com to set up new
          MFA methods"
        output_of_ms_mfa_deletions: data.ms_mfa_delete_response
        output_of_mfa_phone_deletions: data.mfa_delete_response