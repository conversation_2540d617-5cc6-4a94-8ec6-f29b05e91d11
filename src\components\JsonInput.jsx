import React, { useState } from 'react'
import AceEditor from 'react-ace'
import yaml from 'js-yaml'
import toast from 'react-hot-toast'
import { convertToMoveworksYaml, isMoveworksCompatible, isLicenseData, debugReturnSteps } from '../utils/moveworksYamlFormatter'
import { parseLicenseData, generateLicenseYaml } from '../utils/licenseDataParser'

import 'ace-builds/src-noconflict/mode-json'
import 'ace-builds/src-noconflict/theme-github'
import 'ace-builds/src-noconflict/theme-monokai'

function JsonInput({ value, onChange, onConvert, onLicenseData }) {
  const [conversionMode, setConversionMode] = useState('standard')
  const handleConvert = () => {
    try {
      if (!value.trim()) {
        toast.error('Please enter some JSON to convert')
        return
      }

      // Parse JSON to validate it
      const jsonObject = JSON.parse(value)

      let yamlString

      if (conversionMode === 'moveworks') {
        // Debug return steps before conversion
        const returnIssues = debugReturnSteps(jsonObject)
        if (returnIssues.length > 0) {
          console.warn('Return step issues detected:', returnIssues)
          returnIssues.forEach(issue => {
            console.warn(`Path: ${issue.path}, Issue: ${issue.issue}`, issue.value)
          })
        }

        // Convert to Moveworks Compound Action format
        yamlString = convertToMoveworksYaml(jsonObject, {
          addComments: true,
          addProgressUpdates: true
        })
        toast.success('JSON converted to Moveworks Compound Action YAML!')

        // Check if this is license data for table display
        if (isLicenseData(jsonObject)) {
          const parsedLicenses = parseLicenseData(jsonObject)
          // Pass license data to parent component for table display
          if (onLicenseData) {
            onLicenseData(parsedLicenses)
          }
        } else {
          // Clear license data if not license JSON
          if (onLicenseData) {
            onLicenseData(null)
          }
        }
      } else {
        // Check if this is license data for standard conversion
        if (isLicenseData(jsonObject)) {
          const parsedLicenses = parseLicenseData(jsonObject)
          yamlString = generateLicenseYaml(parsedLicenses)

          // Pass license data to parent component
          if (onLicenseData) {
            onLicenseData(parsedLicenses)
          }

          toast.success('Microsoft 365 license data converted to YAML!')
        } else {
          // Standard YAML conversion
          yamlString = yaml.dump(jsonObject, {
            indent: 2,
            lineWidth: -1,
            noRefs: true,
            sortKeys: false
          })
          toast.success('JSON converted to YAML successfully!')

          // Clear license data if not license JSON
          if (onLicenseData) {
            onLicenseData(null)
          }
        }
      }

      onConvert(yamlString)
    } catch (error) {
      console.error('JSON conversion error:', error)

      // Provide more specific error messages
      if (error.message.includes('steps.return')) {
        toast.error(`Return step error: ${error.message}. Return steps must be objects with output_mapper.`)
      } else if (error.message.includes('Unexpected token')) {
        toast.error(`JSON syntax error: ${error.message}. Check for missing commas, quotes, or brackets.`)
      } else if (error.message.includes('Expected')) {
        toast.error(`Type validation error: ${error.message}`)
      } else {
        toast.error(`Invalid JSON: ${error.message}`)
      }
    }
  }

  const handleModeChange = (mode) => {
    setConversionMode(mode)

    // Show helpful message about the selected mode
    if (mode === 'moveworks') {
      toast.success('Moveworks mode: Will format as Compound Action YAML')
    } else {
      toast.success('Standard mode: Will format as generic YAML')
    }
  }

  const handlePasteExample = () => {
    let exampleJson

    if (conversionMode === 'moveworks') {
      // Moveworks Compound Action example
      exampleJson = {
        "user_request": {
          "email": "<EMAIL>",
          "message": "Please reset my password and send me a notification"
        },
        "workflow": {
          "lookup_user": {
            "action_name": "mw.get_user_by_email",
            "user_email": "<EMAIL>"
          },
          "reset_password": {
            "action_name": "reset_user_password",
            "user_id": "user_lookup_result.user.id"
          },
          "send_notification": {
            "action_name": "mw.send_plaintext_chat_notification",
            "message": "Your password has been reset successfully"
          }
        }
      }
    } else {
      // Standard JSON example
      exampleJson = {
        "user": {
          "id": 12345,
          "name": "John Doe",
          "email": "<EMAIL>",
          "profile": {
            "age": 30,
            "location": "New York",
            "interests": ["programming", "music", "travel"]
          }
        },
        "posts": [
          {
            "id": 1,
            "title": "My First Post",
            "content": "This is my first blog post!",
            "tags": ["introduction", "personal"],
            "published": true,
            "created_at": "2024-01-15T10:30:00Z"
          },
          {
            "id": 2,
            "title": "Learning React",
            "content": "Today I learned about React hooks...",
            "tags": ["programming", "react", "learning"],
            "published": false,
            "created_at": "2024-01-16T14:20:00Z"
          }
        ],
        "metadata": {
          "total_posts": 2,
          "last_login": "2024-01-16T15:45:00Z",
          "preferences": {
            "theme": "dark",
            "notifications": true,
            "language": "en"
          }
        }
      }
    }

    onChange(JSON.stringify(exampleJson, null, 2))
    toast.success(`${conversionMode === 'moveworks' ? 'Moveworks' : 'Standard'} example JSON loaded!`)
  }

  const handlePasteLicenseExample = () => {
    // Microsoft 365 license data example
    const licenseExample = {
      "value": [
        {
          "skuId": "c7df2760-2c81-4ef7-b578-5b5392b571df",
          "skuPartNumber": "ENTERPRISEPREMIUM",
          "displayName": "Microsoft 365 E5",
          "prepaidUnits": {
            "enabled": 100,
            "suspended": 0,
            "warning": 0
          },
          "consumedUnits": 85,
          "servicePlans": []
        },
        {
          "skuId": "6fd2c87f-b296-42f0-b197-1e91e994b900",
          "skuPartNumber": "ENTERPRISEPACK",
          "displayName": "Microsoft 365 E3",
          "prepaidUnits": {
            "enabled": 50,
            "suspended": 0,
            "warning": 0
          },
          "consumedUnits": 45,
          "servicePlans": []
        },
        {
          "skuId": "f245ecc8-75af-4f8e-b61f-27d8114de5f3",
          "skuPartNumber": "SPE_E3",
          "displayName": "Microsoft 365 E3 (no Teams)",
          "prepaidUnits": {
            "enabled": 25,
            "suspended": 0,
            "warning": 0
          },
          "consumedUnits": 20,
          "servicePlans": []
        }
      ]
    }

    onChange(JSON.stringify(licenseExample, null, 2))
    toast.success('Microsoft 365 license example loaded!')
  }

  return (
    <div className="json-input">
      <div className="section-header">
        <h2>JSON Input</h2>
        <div className="button-group">
          <button onClick={handlePasteExample} className="btn btn-secondary">
            Load Example
          </button>
          <button onClick={handlePasteLicenseExample} className="btn btn-secondary">
            📊 License Example
          </button>
          <button onClick={handleConvert} className="btn btn-primary">
            Convert to YAML
          </button>
        </div>
      </div>

      {/* Conversion Mode Selection */}
      <div className="conversion-mode-controls">
        <h4>🎯 Conversion Mode</h4>
        <div className="mode-options">
          <label className="mode-option">
            <input
              type="radio"
              name="conversionMode"
              value="standard"
              checked={conversionMode === 'standard'}
              onChange={(e) => handleModeChange(e.target.value)}
            />
            <span className="mode-label">
              📄 Standard YAML
              <small>Generic YAML conversion</small>
            </span>
          </label>
          <label className="mode-option">
            <input
              type="radio"
              name="conversionMode"
              value="moveworks"
              checked={conversionMode === 'moveworks'}
              onChange={(e) => handleModeChange(e.target.value)}
            />
            <span className="mode-label">
              🤖 Moveworks Compound Action
              <small>Formatted for Moveworks platform</small>
            </span>
          </label>
        </div>
      </div>

      <AceEditor
        mode="json"
        theme="github"
        value={value}
        onChange={onChange}
        name="json-input-editor"
        editorProps={{ $blockScrolling: true }}
        width="100%"
        height="400px"
        fontSize={14}
        showPrintMargin={false}
        showGutter={true}
        highlightActiveLine={true}
        setOptions={{
          enableBasicAutocompletion: true,
          enableLiveAutocompletion: true,
          enableSnippets: false,
          showLineNumbers: true,
          tabSize: 2,
          useWorker: false
        }}
        placeholder="Paste your JSON here..."
      />
    </div>
  )
}

export default JsonInput
