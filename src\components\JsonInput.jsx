import React from 'react'
import AceEditor from 'react-ace'
import toast from 'react-hot-toast'
import { convertToMoveworksYaml, isLicenseData, debugReturnSteps } from '../utils/moveworksYamlFormatter'
import { parseLicenseData } from '../utils/licenseDataParser'

import 'ace-builds/src-noconflict/mode-json'
import 'ace-builds/src-noconflict/theme-github'
import 'ace-builds/src-noconflict/theme-monokai'

function JsonInput({ value, onChange, onConvert, onLicenseData }) {
  const handleConvert = () => {
    try {
      if (!value.trim()) {
        toast.error('Please enter some JSON to convert')
        return
      }

      // Parse JSON to validate it
      const jsonObject = JSON.parse(value)

      // Debug return steps before conversion
      const returnIssues = debugReturnSteps(jsonObject)
      if (returnIssues.length > 0) {
        console.warn('Return step issues detected:', returnIssues)
        returnIssues.forEach(issue => {
          console.warn(`Path: ${issue.path}, Issue: ${issue.issue}`, issue.value)
        })
      }

      // Always convert to Moveworks Compound Action format
      const yamlString = convertToMoveworksYaml(jsonObject, {
        addComments: true,
        addProgressUpdates: true
      })

      // Check if this is license data for table display
      if (isLicenseData(jsonObject)) {
        const parsedLicenses = parseLicenseData(jsonObject)
        // Pass license data to parent component for table display
        if (onLicenseData) {
          onLicenseData(parsedLicenses)
        }
      } else {
        // Clear license data if not license JSON
        if (onLicenseData) {
          onLicenseData(null)
        }
      }

      toast.success('JSON converted to Moveworks Compound Action YAML!')
      onConvert(yamlString)
    } catch (error) {
      console.error('JSON conversion error:', error)

      // Provide more specific error messages
      if (error.message.includes('steps.return')) {
        toast.error(`Return step error: ${error.message}. Return steps must be objects with output_mapper.`)
      } else if (error.message.includes('Unexpected token')) {
        toast.error(`JSON syntax error: ${error.message}. Check for missing commas, quotes, or brackets.`)
      } else if (error.message.includes('Expected')) {
        toast.error(`Type validation error: ${error.message}`)
      } else {
        toast.error(`Invalid JSON: ${error.message}`)
      }
    }
  }

  const handlePasteExample = () => {
    // Moveworks Compound Action example
    const exampleJson = {
      "user_request": {
        "email": "<EMAIL>",
        "message": "Please reset my password and send me a notification"
      },
      "workflow": {
        "lookup_user": {
          "action_name": "mw.get_user_by_email",
          "user_email": "<EMAIL>"
        },
        "reset_password": {
          "action_name": "reset_user_password",
          "user_id": "user_lookup_result.user.id"
        },
        "send_notification": {
          "action_name": "mw.send_plaintext_chat_notification",
          "message": "Your password has been reset successfully"
        }
      }
    }

    onChange(JSON.stringify(exampleJson, null, 2))
    toast.success('Moveworks example JSON loaded!')
  }

  const handlePasteLicenseExample = () => {
    // Microsoft 365 license data example
    const licenseExample = {
      "value": [
        {
          "skuId": "c7df2760-2c81-4ef7-b578-5b5392b571df",
          "skuPartNumber": "ENTERPRISEPREMIUM",
          "displayName": "Microsoft 365 E5",
          "prepaidUnits": {
            "enabled": 100,
            "suspended": 0,
            "warning": 0
          },
          "consumedUnits": 85,
          "servicePlans": []
        },
        {
          "skuId": "6fd2c87f-b296-42f0-b197-1e91e994b900",
          "skuPartNumber": "ENTERPRISEPACK",
          "displayName": "Microsoft 365 E3",
          "prepaidUnits": {
            "enabled": 50,
            "suspended": 0,
            "warning": 0
          },
          "consumedUnits": 45,
          "servicePlans": []
        },
        {
          "skuId": "f245ecc8-75af-4f8e-b61f-27d8114de5f3",
          "skuPartNumber": "SPE_E3",
          "displayName": "Microsoft 365 E3 (no Teams)",
          "prepaidUnits": {
            "enabled": 25,
            "suspended": 0,
            "warning": 0
          },
          "consumedUnits": 20,
          "servicePlans": []
        }
      ]
    }

    onChange(JSON.stringify(licenseExample, null, 2))
    toast.success('Microsoft 365 license example loaded!')
  }

  return (
    <div className="json-input">
      <div className="section-header">
        <h2>🤖 JSON to Moveworks YAML</h2>
        <div className="button-group">
          <button onClick={handlePasteExample} className="btn btn-secondary">
            📝 Load Example
          </button>
          <button onClick={handlePasteLicenseExample} className="btn btn-secondary">
            📊 License Example
          </button>
          <button onClick={handleConvert} className="btn btn-primary">
            🚀 Convert to Moveworks YAML
          </button>
        </div>
      </div>

      <AceEditor
        mode="json"
        theme="github"
        value={value}
        onChange={onChange}
        name="json-input-editor"
        editorProps={{ $blockScrolling: true }}
        width="100%"
        height="400px"
        fontSize={14}
        showPrintMargin={false}
        showGutter={true}
        highlightActiveLine={true}
        setOptions={{
          enableBasicAutocompletion: true,
          enableLiveAutocompletion: true,
          enableSnippets: false,
          showLineNumbers: true,
          tabSize: 2,
          useWorker: false
        }}
        placeholder="Paste your JSON here..."
      />
    </div>
  )
}

export default JsonInput
