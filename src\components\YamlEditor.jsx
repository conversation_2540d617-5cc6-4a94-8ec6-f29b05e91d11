import React, { useState, useEffect, useRef, useCallback } from 'react'
import AceEditor from 'react-ace'
import yaml from 'js-yaml'
import toast from 'react-hot-toast'
import { validateMoveworksYaml, getMoveworksSuggestions, convertToMoveworksYaml, fixYamlStructureIssues } from '../utils/moveworksYamlFormatter'

import 'ace-builds/src-noconflict/mode-yaml'
import 'ace-builds/src-noconflict/theme-monokai'
import 'ace-builds/src-noconflict/ext-language_tools'

function YamlEditor({ value, onChange, onGenerateNaturalLanguage }) {
  const [suggestions, setSuggestions] = useState([])
  const [currentSuggestion, setCurrentSuggestion] = useState('')
  const [autoGenerateEnabled, setAutoGenerateEnabled] = useState(true)
  const [livePreview, setLivePreview] = useState('')
  const [moveworksValidation, setMoveworksValidation] = useState(null)

  // Always in Moveworks mode
  const moveworksMode = true
  const editorRef = useRef(null)
  const debounceRef = useRef(null)

  // AI-powered suggestions based on YAML patterns
  const generateSuggestions = useCallback((yamlContent, cursorPosition) => {
    try {
      const lines = yamlContent.split('\n')
      const currentLineIndex = cursorPosition.row
      const currentLine = lines[currentLineIndex] || ''
      const indentLevel = currentLine.match(/^(\s*)/)[1].length

      const suggestions = []

      // Context-aware suggestions based on current structure
      if (currentLine.trim() === '') {
        // Empty line suggestions
        suggestions.push(
          { text: 'name: ', description: 'Add a name property' },
          { text: 'id: ', description: 'Add an identifier' },
          { text: 'type: ', description: 'Add a type specification' },
          { text: 'description: ', description: 'Add a description' },
          { text: 'items:', description: 'Start a list/array' },
          { text: 'properties:', description: 'Start an object/map' }
        )
      } else if (currentLine.includes(':') && !currentLine.includes(' ')) {
        // After a key, suggest common values
        const key = currentLine.split(':')[0].trim().toLowerCase()
        if (key.includes('type')) {
          suggestions.push(
            { text: ' string', description: 'String data type' },
            { text: ' number', description: 'Numeric data type' },
            { text: ' boolean', description: 'Boolean data type' },
            { text: ' array', description: 'Array/list data type' },
            { text: ' object', description: 'Object/map data type' }
          )
        } else if (key.includes('status')) {
          suggestions.push(
            { text: ' active', description: 'Active status' },
            { text: ' inactive', description: 'Inactive status' },
            { text: ' pending', description: 'Pending status' },
            { text: ' completed', description: 'Completed status' }
          )
        }
      }

      setSuggestions(suggestions)
    } catch (error) {
      setSuggestions([])
    }
  }, [])

  // Smart formatting suggestions
  const getFormattingSuggestions = useCallback((yamlContent) => {
    try {
      const parsed = yaml.load(yamlContent)
      const suggestions = []

      // Analyze structure and suggest improvements
      if (typeof parsed === 'object' && parsed !== null) {
        const keys = Object.keys(parsed)

        // Suggest consistent naming conventions
        const hasInconsistentNaming = keys.some(key =>
          key.includes('_') && keys.some(otherKey =>
            otherKey.includes('-') || /[A-Z]/.test(otherKey)
          )
        )

        if (hasInconsistentNaming) {
          suggestions.push('Consider using consistent naming convention (snake_case, kebab-case, or camelCase)')
        }

        // Suggest adding metadata
        if (!keys.includes('version') && !keys.includes('metadata')) {
          suggestions.push('Consider adding version or metadata information')
        }

        // Suggest documentation
        if (!keys.includes('description') && !keys.includes('docs')) {
          suggestions.push('Consider adding description for better AI interpretation')
        }
      }

      return suggestions
    } catch (error) {
      return ['Invalid YAML syntax - check for proper indentation and structure']
    }
  }, [])

  // Enhanced natural language generation with AI insights
  const generateAdvancedNaturalLanguage = useCallback((obj, depth = 0, context = '') => {
    const indent = '  '.repeat(depth)
    let result = ''

    if (Array.isArray(obj)) {
      result += `${indent}📋 This is a collection of ${obj.length} items${context ? ` in ${context}` : ''}:\n`
      obj.forEach((item, index) => {
        if (typeof item === 'object' && item !== null) {
          const itemKeys = Object.keys(item)
          result += `${indent}  • Item ${index + 1}: Contains ${itemKeys.length} properties (${itemKeys.slice(0, 3).join(', ')}${itemKeys.length > 3 ? '...' : ''})\n`
          result += generateAdvancedNaturalLanguage(item, depth + 2, `item ${index + 1}`)
        } else {
          result += `${indent}  • Item ${index + 1}: ${item} (${typeof item})\n`
        }
      })
    } else if (typeof obj === 'object' && obj !== null) {
      const keys = Object.keys(obj)
      result += `${indent}🏗️  This is a structured object with ${keys.length} properties${context ? ` for ${context}` : ''}:\n`

      // Categorize properties for better AI interpretation
      const identifiers = keys.filter(k => k.toLowerCase().includes('id') || k.toLowerCase().includes('key'))
      const metadata = keys.filter(k => ['created', 'updated', 'modified', 'timestamp', 'date'].some(term => k.toLowerCase().includes(term)))
      const descriptive = keys.filter(k => ['name', 'title', 'description', 'label'].some(term => k.toLowerCase().includes(term)))
      const status = keys.filter(k => ['status', 'state', 'active', 'enabled'].some(term => k.toLowerCase().includes(term)))

      if (identifiers.length > 0) {
        result += `${indent}  🔑 Identifiers: ${identifiers.join(', ')}\n`
      }
      if (descriptive.length > 0) {
        result += `${indent}  📝 Descriptive fields: ${descriptive.join(', ')}\n`
      }
      if (status.length > 0) {
        result += `${indent}  ⚡ Status/State fields: ${status.join(', ')}\n`
      }
      if (metadata.length > 0) {
        result += `${indent}  📅 Metadata/Timestamps: ${metadata.join(', ')}\n`
      }

      keys.forEach(key => {
        const value = obj[key]
        if (typeof value === 'object') {
          result += `${indent}  📂 ${key}: ${generateAdvancedNaturalLanguage(value, depth + 1, key)}`
        } else {
          const valueType = typeof value
          const valuePreview = String(value).length > 50 ? String(value).substring(0, 50) + '...' : value
          result += `${indent}  📄 ${key}: "${valuePreview}" (${valueType})\n`
        }
      })
    } else {
      return `${obj} (${typeof obj})`
    }

    return result
  }, [])

  const handleGenerateNaturalLanguage = () => {
    try {
      if (!value.trim()) {
        toast.error('Please enter some YAML to interpret')
        return
      }

      // Parse YAML to validate it
      const yamlObject = yaml.load(value)

      // Generate enhanced natural language interpretation
      const naturalLanguage = generateAdvancedNaturalLanguage(yamlObject)

      // Add AI interpretation insights
      const insights = generateAIInsights(yamlObject)
      const fullInterpretation = naturalLanguage + '\n\n🤖 AI Interpretation Insights:\n' + insights

      onGenerateNaturalLanguage(fullInterpretation)
      toast.success('Enhanced AI interpretation generated!')
    } catch (error) {
      toast.error(`Invalid YAML: ${error.message}`)
    }
  }

  // Generate AI insights about the data structure
  const generateAIInsights = useCallback((obj) => {
    let insights = ''

    try {
      const jsonString = JSON.stringify(obj)
      const complexity = jsonString.length

      if (complexity < 100) {
        insights += '• Simple data structure - easy for AI to process and understand\n'
      } else if (complexity < 1000) {
        insights += '• Moderate complexity - good balance of detail and readability\n'
      } else {
        insights += '• Complex data structure - may need chunking for optimal AI processing\n'
      }

      // Analyze data patterns
      if (Array.isArray(obj)) {
        insights += `• Array-based structure - suitable for batch processing and iteration\n`
      } else if (typeof obj === 'object') {
        const keys = Object.keys(obj)
        if (keys.some(k => k.includes('id'))) {
          insights += '• Contains identifiers - good for entity recognition and relationships\n'
        }
        if (keys.some(k => ['created', 'updated', 'timestamp'].some(term => k.includes(term)))) {
          insights += '• Includes temporal data - useful for time-series analysis\n'
        }
        if (keys.some(k => ['name', 'title', 'description'].some(term => k.includes(term)))) {
          insights += '• Contains descriptive text - suitable for NLP and semantic analysis\n'
        }
      }

      insights += '• Recommended for: Data extraction, pattern recognition, automated documentation'

    } catch (error) {
      insights = 'Unable to generate insights - check data structure'
    }

    return insights
  }, [])

  // Dynamic live preview generation
  const updateLivePreview = useCallback((yamlContent) => {
    if (!autoGenerateEnabled || !yamlContent.trim()) {
      setLivePreview('')
      return
    }

    try {
      const yamlObject = yaml.load(yamlContent)
      const preview = generateAdvancedNaturalLanguage(yamlObject)
      setLivePreview(preview)
    } catch (error) {
      setLivePreview(`⚠️ Syntax Error: ${error.message}`)
    }
  }, [autoGenerateEnabled, generateAdvancedNaturalLanguage])

  // Debounced live preview update
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    debounceRef.current = setTimeout(() => {
      updateLivePreview(value)
    }, 500) // 500ms debounce

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [value, updateLivePreview])

  // Custom auto-completer for YAML
  const yamlCompleter = {
    getCompletions: function(editor, session, pos, prefix, callback) {
      const line = session.getLine(pos.row)
      const completions = []

      // Context-aware completions
      if (line.trim() === '' || line.endsWith(':')) {
        // Common YAML keys
        const commonKeys = [
          'name', 'id', 'type', 'description', 'version', 'status', 'created_at', 'updated_at',
          'properties', 'items', 'metadata', 'config', 'settings', 'data', 'content',
          'title', 'author', 'tags', 'category', 'priority', 'active', 'enabled'
        ]

        commonKeys.forEach(key => {
          completions.push({
            caption: key,
            value: key + ': ',
            meta: 'property',
            score: 1000
          })
        })
      }

      // Value suggestions based on key
      const keyMatch = line.match(/(\w+):\s*$/)
      if (keyMatch) {
        const key = keyMatch[1].toLowerCase()

        if (key.includes('type')) {
          ['string', 'number', 'boolean', 'array', 'object', 'null'].forEach(type => {
            completions.push({
              caption: type,
              value: type,
              meta: 'type',
              score: 900
            })
          })
        } else if (key.includes('status') || key.includes('state')) {
          ['active', 'inactive', 'pending', 'completed', 'draft', 'published'].forEach(status => {
            completions.push({
              caption: status,
              value: status,
              meta: 'status',
              score: 900
            })
          })
        } else if (key.includes('priority')) {
          ['low', 'medium', 'high', 'critical'].forEach(priority => {
            completions.push({
              caption: priority,
              value: priority,
              meta: 'priority',
              score: 900
            })
          })
        }
      }

      callback(null, completions)
    }
  }

  // Handle editor changes with adaptive features
  const handleEditorChange = (newValue) => {
    onChange(newValue)

    // Get cursor position for context-aware suggestions
    if (editorRef.current) {
      const editor = editorRef.current.editor
      const cursorPosition = editor.getCursorPosition()
      generateSuggestions(newValue, cursorPosition)
    }
  }

  // Auto-format YAML
  const handleAutoFormat = () => {
    try {
      if (!value.trim()) {
        toast.error('Please enter some YAML to format')
        return
      }

      const parsed = yaml.load(value)
      const formatted = yaml.dump(parsed, {
        indent: 2,
        lineWidth: -1,
        noRefs: true,
        sortKeys: false
      })

      onChange(formatted)
      toast.success('YAML formatted successfully!')
    } catch (error) {
      toast.error(`Cannot format invalid YAML: ${error.message}`)
    }
  }

  const handleValidateYaml = () => {
    try {
      if (!value.trim()) {
        toast.error('Please enter some YAML to validate')
        return
      }

      yaml.load(value)

      if (moveworksMode) {
        // Validate as Moveworks Compound Action
        const validation = validateMoveworksYaml(value)
        setMoveworksValidation(validation)

        if (validation.isValid) {
          toast.success('YAML is valid Moveworks Compound Action!')
          const moveworksSuggestions = getMoveworksSuggestions(value)
          if (moveworksSuggestions.length > 0) {
            setCurrentSuggestion(moveworksSuggestions.map(s => `${s.type.toUpperCase()}: ${s.message}`).join('\n'))
          } else {
            setCurrentSuggestion('')
          }
        } else {
          toast.error('YAML has Moveworks validation issues')
          setCurrentSuggestion(validation.issues.join('\n'))
        }
      } else {
        // Standard YAML validation
        const suggestions = getFormattingSuggestions(value)
        if (suggestions.length > 0) {
          toast.success('YAML is valid! See suggestions below.')
          setCurrentSuggestion(suggestions.join('\n'))
        } else {
          toast.success('YAML is valid and well-formatted!')
          setCurrentSuggestion('')
        }
      }
    } catch (error) {
      toast.error(`Invalid YAML: ${error.message}`)
    }
  }

  // Fix common YAML structure issues
  const handleFixYamlIssues = () => {
    try {
      if (!value.trim()) {
        toast.error('Please enter some YAML to fix')
        return
      }

      const result = fixYamlStructureIssues(value)

      if (result.error) {
        toast.error(`Error fixing YAML: ${result.error}`)
        return
      }

      if (result.hasChanges) {
        onChange(result.fixedYaml)
        toast.success(`Fixed ${result.fixes.length} issue(s): ${result.fixes.join(', ')}`)
        setCurrentSuggestion('Fixes applied:\n' + result.fixes.map(fix => `• ${fix}`).join('\n'))
      } else {
        toast.info('No common YAML structure issues found')
        setCurrentSuggestion('')
      }
    } catch (error) {
      toast.error(`Error fixing YAML: ${error.message}`)
    }
  }



  // Setup editor with custom completer
  useEffect(() => {
    if (editorRef.current) {
      const editor = editorRef.current.editor
      const langTools = window.ace.require('ace/ext/language_tools')
      langTools.addCompleter(yamlCompleter)
    }
  }, [yamlCompleter])

  return (
    <div className="yaml-editor">
      <div className="section-header">
        <h2>🤖 Moveworks YAML Editor</h2>
        <div className="button-group">
          <button onClick={handleAutoFormat} className="btn btn-secondary">
            🎨 Auto Format
          </button>
          <button onClick={handleValidateYaml} className="btn btn-secondary">
            🔍 Validate Moveworks
          </button>
          <button onClick={handleFixYamlIssues} className="btn btn-warning">
            🔧 Fix Issues
          </button>
          <button onClick={handleGenerateNaturalLanguage} className="btn btn-primary">
            🧠 Generate AI Interpretation
          </button>
        </div>
      </div>

      {/* Adaptive Features Controls */}
      <div className="adaptive-controls">
        <label className="toggle-control">
          <input
            type="checkbox"
            checked={autoGenerateEnabled}
            onChange={(e) => setAutoGenerateEnabled(e.target.checked)}
          />
          <span className="toggle-label">🔄 Live AI Preview</span>
        </label>
        <div className="mode-indicator">
          <span className="mode-badge">🚀 Moveworks Mode Active</span>
        </div>
      </div>

      {/* Moveworks Validation Status */}
      {moveworksMode && moveworksValidation && (
        <div className={`moveworks-validation ${moveworksValidation.isValid ? 'valid' : 'invalid'}`}>
          <h4>🚀 Moveworks Compound Action Status</h4>
          <div className="validation-status">
            {moveworksValidation.isValid ? (
              <span className="status-valid">✅ Valid Compound Action</span>
            ) : (
              <span className="status-invalid">❌ Validation Issues Found</span>
            )}
          </div>
          {moveworksValidation.issues && moveworksValidation.issues.length > 0 && (
            <div className="validation-issues">
              <h5>Issues to Fix:</h5>
              <ul>
                {moveworksValidation.issues.map((issue, index) => (
                  <li key={index}>{issue}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Smart Suggestions Panel */}
      {(currentSuggestion || suggestions.length > 0) && (
        <div className="suggestions-panel">
          <h4>💡 Smart Suggestions</h4>
          {currentSuggestion && (
            <div className="formatting-suggestions">
              <pre>{currentSuggestion}</pre>
            </div>
          )}
          {suggestions.length > 0 && (
            <div className="context-suggestions">
              {suggestions.map((suggestion, index) => (
                <div key={index} className="suggestion-item">
                  <span className="suggestion-text">{suggestion.text}</span>
                  <span className="suggestion-desc">{suggestion.description}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      <AceEditor
        ref={editorRef}
        mode="yaml"
        theme="monokai"
        value={value}
        onChange={handleEditorChange}
        name="yaml-editor"
        editorProps={{ $blockScrolling: true }}
        width="100%"
        height="400px"
        fontSize={14}
        showPrintMargin={false}
        showGutter={true}
        highlightActiveLine={true}
        setOptions={{
          enableBasicAutocompletion: true,
          enableLiveAutocompletion: true,
          enableSnippets: true,
          showLineNumbers: true,
          tabSize: 2,
          useWorker: false,
          wrap: true
        }}
        placeholder="Edit your YAML here... (Ctrl+Space for suggestions)"
      />

      {/* Live Preview Panel */}
      {autoGenerateEnabled && livePreview && (
        <div className="live-preview-panel">
          <h4>🔍 Live AI Interpretation</h4>
          <div className="live-preview-content">
            <pre>{livePreview}</pre>
          </div>
        </div>
      )}
    </div>
  )
}

export default YamlEditor
