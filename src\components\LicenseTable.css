.license-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
  overflow: hidden;
}

.license-table-container .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.license-table-container .section-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.license-table-wrapper {
  overflow-x: auto;
}

.license-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.license-table thead {
  background: #34495e;
  color: white;
}

.license-table th {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.license-table th.header-name {
  width: 40%;
}

.license-table th.header-available-licenses {
  width: 20%;
  text-align: center;
}

.license-table th.header-assigned-licenses {
  width: 25%;
  text-align: center;
}

.license-table th.header-account-type {
  width: 15%;
  text-align: center;
}

.license-table tbody tr {
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.license-table tbody tr:hover {
  background-color: #f8f9fa;
}

.license-table tbody tr:last-child {
  border-bottom: none;
}

.license-table td {
  padding: 12px 16px;
  vertical-align: middle;
}

.license-name {
  display: flex;
  align-items: center;
  gap: 12px;
}

.license-icon {
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.license-title {
  font-weight: 500;
  color: #2c3e50;
}

.available-licenses {
  text-align: center;
  font-weight: 600;
  color: #34495e;
}

.assigned-licenses {
  text-align: center;
}

.license-usage {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: center;
}

.usage-bar-container {
  width: 80px;
  height: 8px;
  background-color: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.usage-bar {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
  min-width: 2px;
}

.usage-text {
  font-weight: 600;
  color: #2c3e50;
  min-width: 30px;
}

.account-type {
  text-align: center;
}

.account-type-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.account-type-badge.organization {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.account-type-badge.self-service {
  background-color: #fff3e0;
  color: #f57c00;
}

.license-summary {
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  color: #6c757d;
  font-size: 14px;
}

.no-data {
  padding: 40px 20px;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

/* Button styles */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Responsive design */
@media (max-width: 768px) {
  .license-table-container .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .license-table {
    font-size: 12px;
  }
  
  .license-table th,
  .license-table td {
    padding: 8px 12px;
  }
  
  .license-name {
    gap: 8px;
  }
  
  .license-icon {
    font-size: 16px;
    width: 20px;
  }
  
  .usage-bar-container {
    width: 60px;
  }
}
