import React from 'react'
import './LicenseTable.css'

/**
 * Component to display Microsoft 365 license data in a table format
 */
function LicenseTable({ licenseData, onExportYaml }) {
  if (!licenseData || !licenseData.rows || licenseData.rows.length === 0) {
    return (
      <div className="license-table-container">
        <div className="section-header">
          <h2>License Information</h2>
        </div>
        <div className="no-data">
          No license data available. Please provide Microsoft 365 license JSON data.
        </div>
      </div>
    )
  }

  const handleExportYaml = () => {
    if (onExportYaml) {
      onExportYaml()
    }
  }

  return (
    <div className="license-table-container">
      <div className="section-header">
        <h2>Microsoft 365 License Information</h2>
        <div className="button-group">
          <button onClick={handleExportYaml} className="btn btn-primary">
            Export as YAML
          </button>
        </div>
      </div>
      
      <div className="license-table-wrapper">
        <table className="license-table">
          <thead>
            <tr>
              {licenseData.headers.map((header, index) => (
                <th key={index} className={`header-${header.toLowerCase().replace(/\s+/g, '-')}`}>
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {licenseData.rows.map((row, index) => (
              <tr key={row.skuId || index} className="license-row">
                <td className="license-name">
                  <div className="license-icon">
                    {getLicenseIcon(row.name)}
                  </div>
                  <span className="license-title">{row.name}</span>
                </td>
                <td className="available-licenses">
                  {formatLicenseNumber(row.available)}
                </td>
                <td className="assigned-licenses">
                  <div className="license-usage">
                    <div className="usage-bar-container">
                      <div 
                        className="usage-bar" 
                        style={{ 
                          width: `${Math.min(row.usagePercentage, 100)}%`,
                          backgroundColor: getUsageColor(row.usagePercentage)
                        }}
                      ></div>
                    </div>
                    <span className="usage-text">
                      {formatLicenseNumber(row.assigned)}
                    </span>
                  </div>
                </td>
                <td className="account-type">
                  <span className={`account-type-badge ${row.accountType.toLowerCase().replace(/\s+/g, '-')}`}>
                    {row.accountType}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div className="license-summary">
        <p>Total licenses: {licenseData.rows.length}</p>
      </div>
    </div>
  )
}

/**
 * Get appropriate icon for license type
 */
function getLicenseIcon(licenseName) {
  const name = licenseName.toLowerCase()
  
  if (name.includes('azure')) return '🔷'
  if (name.includes('dynamics')) return '⚡'
  if (name.includes('enterprise')) return '🏢'
  if (name.includes('microsoft 365')) return '📊'
  if (name.includes('office')) return '📄'
  if (name.includes('teams')) return '💬'
  if (name.includes('power')) return '⚡'
  if (name.includes('project')) return '📋'
  if (name.includes('visio')) return '📐'
  
  return '📦' // Default icon
}

/**
 * Format license numbers for display
 */
function formatLicenseNumber(number) {
  if (number === 0) return '0'
  if (number >= 1000000) return `${(number / 1000000).toFixed(1)}M`
  if (number >= 1000) return `${(number / 1000).toFixed(0)}K`
  return number.toString()
}

/**
 * Get color for usage bar based on percentage
 */
function getUsageColor(percentage) {
  if (percentage >= 90) return '#e74c3c' // Red for high usage
  if (percentage >= 70) return '#f39c12' // Orange for medium-high usage
  if (percentage >= 50) return '#3498db' // Blue for medium usage
  return '#9b59b6' // Purple for low usage
}

export default LicenseTable
