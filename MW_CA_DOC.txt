Mastering Moveworks Compound Actions: A Developer's Guide
1. Introduction to Moveworks Compound Actions
Moveworks Compound Actions serve as a powerful mechanism for developers to build sophisticated automations within the Moveworks platform. They enable the orchestration of multiple, discrete actions into a single, cohesive workflow, thereby facilitating the execution of complex tasks that extend beyond the capabilities of a single API call or script.
1.1. What are Compound Actions? Purpose and Use Cases.
At their core, Compound Actions are defined as workflows that allow developers to orchestrate multiple individual actions into a unified automation sequence.1 These individual actions can encompass a variety of operations, including HTTP calls to external systems, execution of custom APIthon scripts, or leveraging a rich library of Built-in Actions provided by the Moveworks platform.1 The primary purpose of Compound Actions is to enable the execution of complex tasks by logically combining these diverse actions.
This orchestration capability is crucial for automating multi-step business processes. For instance, a Compound Action could be designed to handle a time-off request in Workday, which might involve validating user eligibility, checking available leave balances, submitting the request, and notifying the user's manager.2 Other examples include retrieving an account's current manager from Salesforce, executing a prompt to generate a mermaid diagram, or sending a tailored notification to a user based on specific triggers or data points.2
The significance of Compound Actions lies in their ability to encapsulate business logic. They transform a series of potentially low-level technical operations (like individual API calls or data transformations) into a higher-level, meaningful, and automated task. This abstraction is fundamental to building scalable and maintainable automations. The "cohesive automation" that Compound Actions provide 1 means that developers can model and execute complete, albeit sometimes small, business processes within a single, manageable unit.
This design philosophy aligns with a broader trend towards more "agentic" AI systems, where the Moveworks bot can perform increasingly complex, goal-oriented tasks autonomously.3 The Moveworks "Agentic Automation Engine" is designed to deploy more powerful plugins, enabling the copilot to accomplish more difficult tasks.5 In this context, Compound Actions can be seen as a foundational element, providing the means by which developers define the sophisticated "actions" or capabilities that an intelligent agent can undertake.6 They allow the platform to move beyond simple command-response interactions towards more elaborate task completion, forming the building blocks for these advanced agentic behaviors.
1.2. Core Components: Actions, Input Variables, Control Flow.
Every Compound Action is constructed from three fundamental types of components 1:
1.	Input Variables: These define the data that the Compound Action requires to execute. They serve as parameters that make the workflow dynamic and reusable in different contexts.
2.	Individual Actions: These are the operational units within the workflow. As mentioned, they can be HTTP requests to external services, custom APIthon scripts for bespoke logic, or pre-built Native Actions offered by Moveworks.
3.	Control Flow Logic: This dictates the order and conditions under which individual actions are executed. It includes constructs such as conditional statements (e.g., switch), loops (e.g., for), and mechanisms for returning results or handling errors.
The clear separation of these components—inputs, operations, and logic—promotes a structured and disciplined approach to automation design. This is analogous to defining functions or methods in traditional programming languages, where input parameters are explicitly declared, the function body contains the core operations, and internal logic dictates the execution path. Such a structure enhances the clarity, modularity, and maintainability of the automations built using Compound Actions, making them easier for developers to understand, debug, and evolve over time.
2. Understanding the Evolution: Pre-April 2025 vs. Post-April 2025
The Moveworks platform, including its approach to Compound Actions, has evolved. A significant transition point is April 2025, with notable differences in how Compound Actions are structured, used, and integrated within the broader plugin architecture. Understanding these changes is critical for developers to build effectively on the current platform.
2.1. Key Architectural and Usage Differences.
Several key distinctions mark the evolution of Compound Actions from pre-April 2025 to post-April 2025 versions of the Plugin Workspace:
•	Promotion to Plugins & Requirement:
o	Pre-April 2025: Compound Actions were the primary mechanism for creating functionality. Developers would always create a Compound Action, even if it contained only a single action. This Compound Action was then "promoted" to become a Plugin, making it accessible to end-users via the bot.1
o	Post-April 2025: Compound Actions are no longer universally required for plugins. Instead, they are considered a component that can be part of a plugin through "Action Activities." Plugins can now be built by chaining actions directly within the plugin definition or can consist of just a single action without an explicit Compound Action wrapper.1
•	Role and Focus:
o	Pre-April 2025: Compound Actions were, in essence, the plugins themselves.
o	Post-April 2025: Compound Actions are described as being "less 'intelligent' and more workflow focused." They are recommended when the goal is to chain together actions to accomplish a determined, often reusable, task inside an Action Activity. For more conversational and dynamic experiences, especially those involving multi-turn interactions with users and complex business process guidance by the Moveworks AI Assistant, chaining actions directly within a plugin is the preferred approach.1 This suggests a shift where the plugin itself handles more of the AI-driven conversational logic, while Compound Actions serve as encapsulated, deterministic workflow engines.
•	Script Action Definition:
o	Pre-April 2025: Script Actions (using APIthon) were defined within a Compound Action.7
o	Post-April 2025: Script Actions can be defined independently. These standalone Script Actions can then be referenced in either Compound Actions or directly in Plugins.7 This modularization allows for greater reusability of custom script logic.
This architectural evolution signifies a maturation of the Moveworks platform, moving towards a more flexible, granular, and powerful development model. The decoupling of the "plugin" (the user-facing conversational unit) from the "compound action" (now more of a reusable workflow component) is a key aspect of this change. Such decoupling allows for simpler and faster creation of plugins for basic tasks, as the overhead of defining a Compound Action is removed. Simultaneously, it reserves Compound Actions for encapsulating more complex, potentially reusable, sequences of logic that can be invoked as needed.
The shift also appears designed to enhance both developer efficiency and the end-user experience. By allowing plugins to directly manage conversational dynamics and leverage AI more fluidly, the interactions can become more natural and responsive. Compound Actions, by becoming more "workflow focused," can be honed as robust, testable, and reusable automation units. These units can then be triggered by the more intelligent plugin front-ends or through the new "Action Activities" construct. This aligns with the objectives of the "Agentic Automation Engine," which aims to enable developers to "build new plugins faster and with less code" and "deploy more powerful plugins that enable the copilot to accomplish more difficult tasks".5
2.2. Impact on Plugin Development Strategy.
These changes necessitate adjustments in how developers approach plugin design and development on the Moveworks platform:
•	Decision Point: CA vs. Direct Action Chaining: In the post-April 2025 paradigm, developers must decide whether a sequence of actions warrants encapsulation within a Compound Action or if it can be more effectively managed by chaining actions directly within a plugin. The guiding principle is often the scope of reusability and the nature of the logic. If a sequence of actions represents a generic, reusable business process (e.g., "create a standard Jira ticket with specific fields populated"), it is a strong candidate for a Compound Action. This CA can then be invoked from multiple plugins or Action Activities. Conversely, if the action sequence is tightly coupled with a specific plugin's conversational flow and is unlikely to be reused elsewhere, defining it directly within the plugin might be more straightforward.
•	Role of "Action Activities": "Action Activities" appear to be the new integration point for incorporating Compound Actions within the broader plugin structure.1 Developers will need to understand how these activities are defined and how they host or invoke Compound Actions. They likely serve as containers within a plugin that can execute one or more CAs or other action types.
•	Reusability: The post-April 2025 model places a stronger emphasis on the potential for Compound Actions to be truly reusable components. By being "workflow focused" rather than tied to a specific plugin's overarching conversational intelligence, CAs can be designed as self-contained automation modules. This modularity is a hallmark of mature development platforms, promoting both efficiency and consistency.
The following table summarizes the key differences and their implications for developers:
Table 1: Key Differences: Pre- vs. Post-April 2025 Compound Actions
Aspect	Pre-April 2025	Post-April 2025	Developer Implication
Requirement in Plugins	Always required; CA promoted to Plugin. 1	Not required; CAs are part of a plugin via "Action Activities" or actions chained directly in plugin. 1	Simplifies development for single-action or simple sequence plugins. Requires more design consideration for when to use a CA.
Primary Role	The core logic and structure of the Plugin. 1	"Workflow focused" component, often for reusable sequences of actions within an Action Activity. 1	CAs become more specialized for backend process automation. Conversational intelligence and dynamic flows are handled more by the plugin itself.
Integration Point	CA is the Plugin.	Integrated into Plugins via "Action Activities". 1	Developers need to learn the "Action Activity" construct as the new way to incorporate CAs.
Script Action Definition	Defined within the Compound Action. 7	Can be defined independently and referenced by CAs or Plugins. 7	Promotes reusability of APIthon scripts across different CAs and plugins.
Conversational Experience	Handled by the overall CA structure.	Plugins are recommended for "conversational and dynamic experiences" with multi-turn interactions. 1	Frees plugins to leverage AI for more natural conversations, while CAs handle deterministic execution.
"Intelligence"	Implicitly carried the "intelligence" of the plugin.	CAs are "less 'intelligent'"; plugins handle more dynamic AI-driven processes. 1	Shifts the focus of CA development to robust workflow execution rather than complex AI interaction logic.
3. Fundamentals of Compound Action YAML
Compound Actions are defined using YAML (YAML Ain't Markup Language), a human-readable data serialization language. Understanding the basic YAML structure is the first step towards building any Compound Action.
3.1. Basic Structure and the steps Key.
The cornerstone of a Compound Action's YAML definition, particularly when multiple operations are involved, is the steps key. The steps key introduces a list, where each item in the list represents an individual action or expression to be executed.8 These steps are performed sequentially in the order they appear in the list.
•	Single Expression Compound Action: For Compound Actions that contain only a single action or expression, using the steps key is optional. The action can be specified directly at the top level of the compound action definition.8 This offers a more concise syntax for simple cases.
•	Multiple Expressions Compound Action: When a Compound Action includes multiple actions or expressions, encapsulating them within a steps list under the steps key is required. This clearly defines the execution order and logically groups the expressions.8
The optionality of steps for single actions streamlines the definition of very simple Compound Actions, reducing unnecessary boilerplate. However, its mandatory use for multiple actions enforces a clear, explicit, and ordered execution flow. This explicit ordering is fundamental to how procedural logic operates and is essential for developers to reason about the Compound Action's behavior, predict its outcomes, and debug it effectively.
A basic example illustrating the steps key with multiple actions:
YAML
steps:
  - action: # First action to be executed
      action_name: example_action_1_name
      output_key: result_from_action1 # Output will be stored here
      input_args:
        example_input_1: "Value for Action 1"
  - action: # Second action, executed after the first one completes
      action_name: example_action_2_name
      output_key: result_from_action2
      input_args:
        example_input_2: "Value for Action 2"
        input_from_previous_step: data.result_from_action1.some_field # Using output from previous step
In this structure, example_action_1_name would execute first. Upon its completion, example_action_2_name would execute, potentially using data produced by the first action. The output_key is used to name the variable that will hold the result of each action, and these results are typically accessible via the data object (e.g., data.result_from_action1). An underscore (_) can be used as an output_key if the output of that specific step is not needed by subsequent steps or for the final result.9
3.2. Defining Input Variables.
Input Variables are crucial for making Compound Actions dynamic, reusable, and capable of operating on different data depending on the context in which they are invoked. These variables define the data that the Compound Action needs to begin its execution.1
When a Compound Action is defined (often through the Creator Studio interface), its input fields are specified. These input variables, once provided during the execution of the Compound Action, are inserted at the top level of the data key within the Compound Action's execution context.9 This means they become readily accessible throughout the various steps of the Compound Action.
For example, if a Compound Action is defined with input variables target_email (a string) and priority_level (a number), these can be accessed within the YAML steps as data.target_email and data.priority_level, respectively.
A conceptual YAML snippet showing how input variables might be used:
YAML
# Assume the Compound Action is defined in Creator Studio with inputs:
# - user_email (string)
# - task_description (string)

steps:
  - action:
      action_name: mw.get_user_by_email # A built-in action
      output_key: user_details_result
      input_args:
        user_email: data.user_email # Accessing the 'user_email' input variable

  - script:
      output_key: formatted_task
      input_args:
        description: data.task_description # Accessing 'task_description' input
        user_name: data.user_details_result.user.full_name # Using data from previous step
      code: |
        # APIthon script to format the task
        formatted = "Task for " + user_name + ": " + description
        formatted # This will be the output of the script

  - action:
      action_name: create_todo_item # A hypothetical HTTP action
      output_key: todo_creation_status
      input_args:
        details: data.formatted_task # Using the output from the script
        assignee_id: data.user_details_result.user.id
This consistent access pattern for input variables (via the data object) provides a standardized and predictable way to reference incoming data throughout the various steps of a Compound Action. This simplifies data flow management and makes the YAML definitions easier to read and understand.
4. Quickstart #4: Compound Actions: Beginner
This quickstart is designed for developers new to Compound Actions, focusing on multi-action integrations and the fundamentals of YAML syntax.
4.1. Your First Compound Action: A Simple Sequence.
The goal here is to create a very basic Compound Action with one or two sequential steps. This will introduce the core YAML structure and the concept of ordered execution.
Focus:
•	Basic YAML structure using the steps key.
•	Defining a single, simple action step.
•	Understanding how output_key stores the result of an action.
•	Accessing results via the data object.
Example Scenario:
A common beginner task is to fetch details for a user given their email and then use a simple script to format a personalized greeting message. This involves:
1.	An input variable for the user's email.
2.	A built-in action to retrieve user information.
3.	A script action to construct the greeting.
Step-by-step Instructions:
1.	Define Input Variable: In your Compound Action definition (e.g., via Creator Studio), create an input variable, say email_address of type string.
2.	YAML Structure:
YAML
steps:
  - action:
      action_name: mw.get_user_by_email # Built-in action to fetch user details
      output_key: fetched_user_info
      input_args:
        user_email: data.email_address # Using the input variable

  - script:
      output_key: greeting_message
      input_args:
        user_full_name: data.fetched_user_info.user.full_name # Accessing a field from the previous action's output
      code: |
        # APIthon code to create a greeting
        greeting = "Hello, " + user_full_name + "! Welcome."
        greeting # This is the return value of the script
In this example, the first step calls mw.get_user_by_email 11, passing the data.email_address input. The result is stored in data.fetched_user_info. The second step, a script action, takes the full_name from data.fetched_user_info.user.full_name as an input argument (user_full_name) and constructs a greeting string, which is then stored in data.greeting_message.
4.2. Integrating Multiple Actions.
Compound Actions excel at orchestrating sequences of different types of actions.
4.2.1. Calling HTTP Actions.
HTTP Actions are fundamental for integrating Moveworks with external business systems and APIs (e.g., ServiceNow, Salesforce, Workday).2 Within a Compound Action, an HTTP Action is invoked by referencing its pre-configured action_name. The actual configuration of the HTTP Action (including endpoint URL, authentication method, headers, etc.) is typically done in the Creator Studio, separate from the Compound Action YAML.6
The YAML syntax for an HTTP action step generally includes:
•	action_name: The unique identifier of the configured HTTP Action.
•	output_key: The variable name to store the response from the HTTP call.
•	input_args: A dictionary of arguments to pass to the HTTP Action (e.g., query parameters, request body components).
•	progress_updates (optional): Messages displayed to the user while the action is pending and upon completion.8
YAML Snippet (Conceptual):
YAML
steps:
  - action:
      action_name: retrieve_customer_order_details # Name of a pre-configured HTTP Action
      output_key: customer_order_response
      input_args:
        order_id: data.input_order_id # Assuming 'input_order_id' is an input variable
        customer_segment: "premium"
      progress_updates:
        on_pending: "Retrieving order details for order ID: {{data.input_order_id}}..."
        on_complete: "Order details retrieved successfully."

  # A subsequent step could use data.customer_order_response
  - script:
      output_key: order_summary
      input_args:
        order_data: data.customer_order_response.body # Assuming response body contains order info
      code: |
        # APIthon to process order_data and create a summary
        summary = "Order Total: $" + str(order_data.total_amount) # Example
        summary
This abstraction of HTTP Action configuration is a significant advantage. It separates the concerns of API connectivity and authentication from the logical flow of the Compound Action. This promotes cleaner CA definitions, makes it easier to manage API credentials securely, and allows the underlying API details to change without necessarily requiring modifications to every Compound Action that uses it. The CA developer simply calls the named action, trusting that its external configuration is correct.
4.2.2. Using Basic Built-in Actions.
Moveworks provides a range of built-in actions, also known as Native Actions, which can be directly invoked within Compound Actions using the mw.{{action_name}} syntax.11 These actions simplify common tasks such as sending chat notifications, retrieving user information, or initiating approval processes.
The mw. namespace serves an important role: it clearly distinguishes Moveworks-provided, standardized actions from custom HTTP or Script actions that a developer might create. This improves the readability of the YAML and signals to the developer that they are using a set of well-defined, platform-supported functionalities.
Example 11:
Assume a previous step in the Compound Action has retrieved a user's record ID and stored it in data.target_user_object.user.id.
YAML
steps:
  #... previous steps...

  - action:
      action_name: mw.send_plaintext_chat_notification
      output_key: chat_notification_status # Stores the result of the send operation
      input_args:
        user_record_id: data.target_user_object.user.id # Target user's record ID
        message: "Your request has been processed. Reference ID: {{data.request_reference_id}}" # Personalized message
      progress_updates:
        on_pending: "Sending notification..."
        on_complete: "Notification sent."
Here, mw.send_plaintext_chat_notification is called to send a message. The user_record_id and message are provided as input arguments. The message itself can be dynamic, incorporating data from other parts of the Compound Action (like data.request_reference_id in this example) using {{}} template syntax.
4.3. Basic YAML for Sequential Logic.
As outlined previously, the steps list is the fundamental construct for defining sequential logic. Actions listed under steps are executed one after another, in the order they appear in the YAML. The completion of one step (successfully or with an error, if not handled) triggers the execution of the next. This straightforward sequential processing is the most basic form of workflow logic in Compound Actions.
4.4. Handling Inputs and Outputs (output_key, basic data access).
Effective data flow is critical for multi-step automations. In Compound Actions, this is primarily managed through output_key and the data object.
•	output_key: Every action or script step that produces a result requires an output_key to be defined.8 This key serves as the variable name under which the step's output (e.g., API response, script return value) will be stored. The mandatory nature of output_key (unless the output is explicitly ignored using _) ensures that all potentially useful results are captured and explicitly named within the Compound Action's context.
•	data Object: The data object acts as a central repository or shared memory for the Compound Action's execution.9 It holds:
o	The initial input variables provided to the Compound Action.
o	The outputs of all completed steps, stored under their respective output_keys.
Subsequent steps in the Compound Action can then access these stored values using the pattern data.your_output_key.some_field_in_the_output or data.your_input_variable. This allows later steps to build upon the results generated by earlier ones, enabling the creation of complex data pipelines and decision-making processes.
Example of Data Flow:
YAML
steps:
  - action:
      action_name: get_initial_record # Assume this action returns { "id": "123", "status": "pending" }
      output_key: step1_result
      input_args:
        record_name: data.input_record_name

  - script:
      action_name: process_record_status
      output_key: step2_processed_data
      input_args:
        current_status: data.step1_result.status # Accessing 'status' from step1_result
        record_identifier: data.step1_result.id # Accessing 'id' from step1_result
      code: |
        # APIthon code
        new_status_message = "Record " + record_identifier + " is currently " + current_status + "."
        new_status_message # This is returned

  - action:
      action_name: mw.send_plaintext_chat_notification
      output_key: notification_status
      input_args:
        user_record_id: data.requestor.record_id # Assuming requestor info is available
        message: data.step2_processed_data # Using the full output from step 2
In this flow, step1_result holds the output of the first action. The script in the second step uses fields from data.step1_result. Finally, the notification action uses the entire output from the script, data.step2_processed_data, as its message.
4.5. Beginner Practical Example: Fetching User Data and Sending a Notification.
This complete example combines concepts from this beginner quickstart to perform a common IT support task: looking up a user and sending them a basic notification.
Scenario: An employee reports an issue, and an automated process needs to acknowledge this by sending them a personalized chat message.
Steps:
1.	Input Variable: The Compound Action will take the user's email address as input. Let's call it user_email_input.
2.	Fetch User Details: Use the mw.get_user_by_email built-in action to retrieve the user's full details, including their record ID (needed for sending a notification) and full name (for personalization).
3.	Send Notification: Use the mw.send_plaintext_chat_notification built-in action to send a message to the user.
YAML Implementation:
YAML
# Input variable defined in Creator Studio:
# - user_email_input (string), e.g., "[[email protected]](https://help.moveworks.com/cdn-cgi/l/email-protection)"

steps:
  - action:
      action_name: mw.get_user_by_email
      output_key: user_lookup_result
      input_args:
        user_email: data.user_email_input # Using the defined input variable
      progress_updates:
        on_pending: "Looking up user details for {{data.user_email_input}}..."
        on_complete: "User details retrieved."

  - action:
      action_name: mw.send_plaintext_chat_notification
      output_key: notification_dispatch_status
      input_args:
        # User record ID is required by send_plaintext_chat_notification
        # It's found within the output of mw.get_user_by_email
        user_record_id: data.user_lookup_result.user.id
        # Constructing a personalized message using the user's full name
        message: $CONCAT(["Hello ", data.user_lookup_result.user.full_name, ", we have received your request and will look into it shortly."], "", TRUE)
        # The $CONCAT is a Moveworks Data Mapping Syntax function.
        # Alternatively, a script action could be used for more complex message formatting.
      progress_updates:
        on_pending: "Sending acknowledgment to {{data.user_lookup_result.user.full_name}}..."
        on_complete: "Acknowledgment sent."

  - return: # Optionally, return a status or summary
      output_mapper:
        final_status: "'Notification sent to ' + data.user_lookup_result.user.full_name"
        user_record_id_processed: data.user_lookup_result.user.id
Explanation:
•	The Compound Action starts by using mw.get_user_by_email with the provided data.user_email_input. The output, containing the user object, is stored in data.user_lookup_result.11
•	The second action, mw.send_plaintext_chat_notification, then uses data.user_lookup_result.user.id to target the correct user and data.user_lookup_result.user.full_name for a personalized message. The message concatenation is shown using $CONCAT, a function from Moveworks Data Mapping Syntax (DSL).12
•	Finally, a return step is included to output a summary of the operation.
This example demonstrates a simple yet complete multi-action integration using basic YAML, input variables, built-in actions, and data flow between steps.
5. Quickstart #5: Compound Actions: Advanced
This quickstart delves into more complex integrations, advanced data manipulation techniques (including APIthon scripting and Moveworks Data Mapping Syntax), and sophisticated workflow logic such as conditionals, loops, parallel processing, and robust error handling.
5.1. Mastering Control Flow.
Beyond simple sequential execution, Compound Actions offer constructs to manage more intricate workflow logic.
5.1.1. Conditional Logic with switch.
The switch expression provides a way to implement conditional branching, akin to if/else-if/else statements in programming languages. It evaluates a series of conditions and executes the steps associated with the first condition that evaluates to true.8
A switch expression consists of:
•	cases: A list of dictionaries. Each dictionary must contain: 
o	condition: A boolean expression that is evaluated. This expression can use data from input variables or previous steps (e.g., data.user_details.access_level == 'admin').
o	steps: A list of action/expression steps to execute if this condition is true.
•	default (optional): A dictionary containing a steps list to be executed if none of the conditions in cases evaluate to true.
YAML Snippet 8:
YAML
steps:
  # Assume a previous step populated data.user_details with an object like
  # { "id": "user123", "access_level": "admin", "department": "Engineering" }

  - switch:
      cases:
        - condition: data.user_details.access_level == 'admin'
          steps:
            - action:
                action_name: grant_admin_privileges
                output_key: admin_privileges_status
                input_args:
                  user_id: data.user_details.id
                  resource: "all_systems"
            - action:
                action_name: mw.send_plaintext_chat_notification
                output_key: admin_notification_status
                input_args:
                  user_record_id: data.user_details.id # Assuming ID is the record_id
                  message: "Admin privileges granted to all systems."
        - condition: data.user_details.access_level == 'editor' && data.user_details.department == 'Marketing'
          steps:
            - action:
                action_name: grant_marketing_editor_access
                output_key: editor_privileges_status
                input_args:
                  user_id: data.user_details.id
                  content_folder: "/marketing/campaigns"
            - action:
                action_name: mw.send_plaintext_chat_notification
                output_key: editor_notification_status
                input_args:
                  user_record_id: data.user_details.id
                  message: "Editor access granted to Marketing campaign content."
      default:
        steps:
          - action:
              action_name: mw.send_plaintext_chat_notification
              output_key: default_notification_status
              input_args:
                user_record_id: data.user_details.id
                message: "Standard access level assigned. No special privileges granted."
          - raise: # Example of raising an informational error/warning in default
              output_key: default_case_notice
              message: "User did not match specific privilege conditions. Standard access applied."
The switch structure provides a clean, readable, and declarative way to implement conditional execution paths. This is often more manageable than deeply nested conditional logic within APIthon scripts, especially when the primary goal is to orchestrate different actions or sequences of actions based on varying conditions.
5.1.2. Iteration with for Loops.
The for loop expression enables iteration over an iterable collection (like a list). For each item in the collection, a defined set of steps can be executed.8 This is essential for processing multiple items, such as a list of users, tickets, or assets.
A for loop requires the following fields:
•	each: A variable name that will represent the current item from the iterable during each iteration.
•	index: A variable name that will represent the zero-based index of the current item during each iteration.
•	in: The name of the iterable variable (usually accessed from the data object, e.g., data.user_list) that the loop will iterate over.
•	output_key: A variable name to store the results of the loop's execution. The value stored will be a list, where each element corresponds to the output(s) of the steps executed for each item in the input iterable.
•	steps (optional): A list of Compound Action expressions to be executed on each element of the loop.
YAML Snippet 8:
YAML
steps:
  # Assume data.employee_list contains a list of employee objects:
  # e.g.,

  - for:
      each: employee_record # 'employee_record' will hold one employee object per iteration
      index: employee_idx # 'employee_idx' will be 0, 1,...
      in: data.employee_list # The list to iterate over
      output_key: salary_adjustment_outcomes # This will be a list of results from the script
      steps:
        - script:
            output_key: adjusted_salary_info # This key is local to the loop's iteration result
            input_args:
              current_salary: employee_record.salary # Pass current employee's salary
              employee_name: employee_record.name
            code: |
              # APIthon script to calculate a 5% raise
              new_salary = current_salary * 1.05
              result_map = {
                "name": employee_name,
                "old_salary": current_salary,
                "new_salary": new_salary,
                "increase_amount": new_salary - current_salary
              }
              result_map # This dictionary will be one element in 'salary_adjustment_outcomes' list

  # After the loop, data.salary_adjustment_outcomes would look like:
  #
  - action: # Example: Log the results or send a summary notification
      action_name: log_salary_adjustments
      output_key: logging_status
      input_args:
        adjustments: data.salary_adjustment_outcomes
The for loop's output_key is particularly powerful as it collects the results from each iteration. If the steps within the loop produce an output (e.g., via their own output_key), this output becomes an element in the list associated with the for loop's output_key. This means the for loop is not just for performing actions with side effects on each item but also for transforming a collection of items into a new collection of results, similar to a map operation in functional programming paradigms.
5.2. Advanced Data Manipulation.
Compound Actions provide robust mechanisms for transforming, shaping, and manipulating data as it flows through a workflow.
5.2.1. Leveraging APIthon script Actions (Deep Dive).
The script expression allows developers to execute custom code using APIthon, a Python-like scripting language, directly within a Compound Action.8 This provides significant flexibility for implementing complex data transformations, custom business logic, or any operation not readily available through built-in actions or data mappers.
A script action minimally requires:
•	code: A string containing the APIthon code to be executed.
•	output_key: A variable name to store the result returned by the script.
•	input_args (optional): A dictionary mapping input argument names (used within the script) to their values, which can be literals or dynamic references to data from the data object.
APIthon Characteristics and Constraints:
APIthon, while Python-based, has specific characteristics and limitations that developers must understand 14:
•	Return Value: The result of the last line of executed code in the APIthon script is what gets returned and stored in the output_key.
•	No Imports: APIthon does not allow import statements. Developers are restricted to built-in Python functions and standard data types.
•	No Class Definitions: Custom class definitions are not supported.
•	No Private Members: Accessing methods or attributes starting with an underscore (_) is generally disallowed.
•	Size Limits: There are constraints on the length of the code (e.g., 4096 bytes), the size of lists (e.g., 2096 bytes), the magnitude of numbers, and the length of strings (e.g., 4096 bytes/characters).14
•	Supported Operations: APIthon supports common operations on strings, lists, sets, dictionaries, numbers, and standard built-in functions like len(), str(), int(), etc..14
YAML Snippet 13:
YAML
steps:
  # Assume data.transaction_values is a list of numbers, e.g., 
  - script:
      output_key: transaction_stats
      input_args:
        numbers: data.transaction_values # Dynamically pass a list of numbers
      code: > # Using '>' for a multi-line script block
        sum_numbers = sum(numbers)
        count_numbers = len(numbers)
        average = 0
        if count_numbers > 0:
            average = sum_numbers / count_numbers
        
        # Find min and max
        min_value = min(numbers) if count_numbers > 0 else 0
        max_value = max(numbers) if count_numbers > 0 else 0
        
        stats_dict = {
          'sum': sum_numbers,
          'count': count_numbers,
          'average': average,
          'minimum': min_value,
          'maximum': max_value
        }
        stats_dict # The last line, this dictionary, is returned
The constraints on APIthon (like no imports and size limits) are important. They encourage developers to use APIthon for focused, targeted logic rather than attempting to build large, monolithic applications within a single script block. This design steers developers towards leveraging other platform features, such as dedicated HTTP actions for API calls or Data Mappers for simpler transformations, reserving APIthon for tasks where its imperative control is truly necessary. The platform documentation explicitly advises to "use DSL and our Data Mapper for common data transformation operations" before resorting to Script Actions, noting that Script Actions can add "non-negligible latency".7
Despite these limitations, APIthon serves as a vital "escape hatch." It acknowledges that declarative YAML and pre-built actions cannot cover every conceivable edge case or complex calculation. It provides developers with imperative control when needed, but its design encourages judicious use. This reflects a common pattern in modern platforms: provide high-level declarative tools for common tasks and more powerful, but constrained, scripting capabilities for specialized requirements. (For a quick summary of APIthon capabilities, refer to Appendix A.3: APIthon Quick Reference).
5.2.2. Utilizing Moveworks Data Mapping Syntax (Bender/DSL).
Moveworks Data Mapping Syntax (often referred to as Bender or DSL - Domain Specific Language) provides a powerful, declarative way to manipulate and transform data directly within the YAML definition of a Compound Action. It is primarily used within the input_args of actions and scripts to shape data before it's passed in, and in the output_mapper of return expressions to structure the final output of a Compound Action.8
This syntax allows for a range of operations including 9:
•	Basic data transformations (e.g., string concatenation, case changes).
•	Conditional logic within mappings.
•	List operations (e.g., mapping, filtering).
•	Type conversions.
While a comprehensive "Bender Language Reference" was not available in the provided materials 19, examples from various snippets illustrate its capabilities.12 Functions like $CONCAT, $MAP, $LOWERCASE, and $TITLECASE are indicative of its features.
Example 8:
YAML
# Assume data.user_list contains:
# [{ "id": "u1", "first_name": "jane", "last_name": "doe", "email": "[[email protected]](https://help.moveworks.com/cdn-cgi/l/email-protection)" },
#  { "id": "u2", "first_name": "john", "last_name": "smith", "email": "[[email protected]](https://help.moveworks.com/cdn-cgi/l/email-protection)" }]
# And data.default_domain is "example.com"

steps:
  - return:
      output_mapper:
        processed_users:
          MAP(): # Iterate over data.user_list
            items: data.user_list
            converter: # Define how each item is transformed
              user_id: item.id
              full_name: $CONCAT(, " ") # Title case and concatenate
              # Example of conditional logic (syntax conceptual):
              # contact_email: $IF(item.email, item.email, $CONCAT([item.first_name, ".", item.last_name, "@", data.default_domain]))
        system_message: "'User processing complete.'"
The Data Mapping Syntax offers a concise and often more readable alternative to writing an APIthon script for common data shaping tasks. If a transformation involves simple field mapping, concatenation, case changes, or basic list manipulations, DSL can achieve this directly in YAML, reducing the need for a separate script block. This can lead to cleaner CA definitions and potentially better performance, as APIthon scripts inherently add some latency.7
The platform's encouragement to "use DSL and our Data Mapper for common data transformation operations" 7 suggests a preference for declarative data handling. Declarative approaches define what outcome is desired, rather than detailing how to achieve it step-by-step. This can allow the Moveworks platform to potentially optimize, validate, or better understand these transformations, which is more challenging with opaque APIthon scripts.
5.3. Orchestrating Complex Workflows.
For more sophisticated automation scenarios, Compound Actions provide constructs for parallel execution and robust error handling.
5.3.1. Parallel Processing with parallel.
The parallel expression enables the concurrent execution of multiple expressions or iterations of a loop. This can significantly optimize Compound Action performance by running independent tasks simultaneously, thereby reducing the overall execution time.8
The parallel construct can be used in two main ways:
1.	With for: To execute the steps for each item in an iterable in parallel. This requires an in field (the iterable), an output_key (to store results), and typically an each variable.
2.	With branches: To execute a predefined list of expressions (actions, scripts, etc.) concurrently. Each branch is an independent flow of execution.
YAML Snippet 8:
YAML
steps:
  # Assume data.requestor.id and data.system_config.api_version are available
  - parallel:
      branches:
        - action: # Branch 1: Fetch user-specific data
            action_name: fetch_user_permissions
            output_key: user_permissions_result
            input_args:
              user_id: data.requestor.id
        - action: # Branch 2: Fetch system-wide configuration
            action_name: fetch_global_settings
            output_key: global_settings_result
            input_args:
              version: data.system_config.api_version
        - script: # Branch 3: Perform an independent calculation
            output_key: background_calculation
            code: |
              # Some time-consuming calculation that doesn't depend on other branches
              result = 0
              for i in range(1000000): # Simulate work
                  result += i
              result
  
  # Subsequent steps, executed after all parallel branches complete,
  # can access data.user_permissions_result, data.global_settings_result, and data.background_calculation
  - action:
      action_name: process_combined_data
      output_key: final_processing_outcome
      input_args:
        permissions: data.user_permissions_result
        settings: data.global_settings_result
        calc_value: data.background_calculation
While parallel is powerful for optimization, it necessitates careful workflow design. Developers must ensure that tasks executed in parallel are genuinely independent or that any dependencies are managed correctly (e.g., by ensuring all parallel branches complete before subsequent steps that rely on their outputs). Incorrectly assuming independence can lead to race conditions or unpredictable behavior if parallel tasks attempt to modify shared state in an uncoordinated manner, though the CA execution model likely mitigates some of these risks by managing state through the data object.
5.3.2. Effective Error Handling: try_catch and raise.
Robust error handling is crucial for building resilient automations that can gracefully manage unexpected issues. Compound Actions provide try_catch and raise expressions for this purpose.
•	try_catch: This expression allows a block of steps (the try block) to be executed. If an error occurs within any of these steps, the execution flow transfers to the catch block.8
o	The catch block can be configured with on_status_code to specify which types of errors (e.g., HTTP status codes like E400, E503, or custom error identifiers) should trigger its execution. If on_status_code is omitted, the catch block will handle any error from the try block.
o	The steps within the catch block define the error handling logic (e.g., logging the error, notifying an administrator, attempting a recovery action, or raising a new, more specific error).
o	Information about the error that occurred in the try block is typically made available within an error_data object 8, accessible within the catch block's steps.
•	raise: This expression explicitly stops the execution of the Compound Action by raising an error.8
o	It requires an output_key where error information will be stored.
o	An optional message can be provided to give context about the error.
o	raise can be used within a catch block to re-raise an error (perhaps after logging it) or to transform an error into a custom error type. It can also be used directly within steps (e.g., inside a switch case) if a condition is met that makes further processing impossible or invalid.
YAML Snippet 8:
YAML
steps:
  - try_catch:
      try:
        steps:
          - action:
              action_name: critical_external_api_call
              output_key: api_call_result
              input_args:
                resource_id: data.target_resource_id
      catch:
        on_status_code: # Catch specific server errors or timeouts
        steps:
          - action: # Log the failure
              action_name: log_critical_failure
              output_key: logging_outcome
              input_args:
                failed_action: "critical_external_api_call"
                # Assuming error details are in error_data.api_call_result
                error_message: error_data.api_call_result.error.message
                resource_id_attempted: data.target_resource_id
          - action: # Notify an admin or support group
              action_name: mw.send_plaintext_chat_notification
              output_key: admin_alert_status
              input_args:
                user_record_id: data.admin_group_record_id # Pre-fetched admin/group ID
                message: $CONCAT(["Critical API 'critical_external_api_call' failed for resource ", data.target_resource_id, ". Error: ", error_data.api_call_result.error.message], "", TRUE)
          - raise: # Re-raise a standardized error to halt the CA and signal failure
              message: "A critical system dependency failed. The operation could not be completed. Administrators have been notified."
              output_key: critical_dependency_failure
The try_catch mechanism, especially with on_status_code, allows for fine-grained error handling strategies. Different types of errors can be routed to different handling logic, enabling more sophisticated recovery or reporting mechanisms than a simple blanket failure.
5.3.3. Using return for Early Exits.
The return expression facilitates a graceful and early exit from a Compound Action. It stops further execution of steps within the current path and provides a specific output, typically defined using an output_mapper.8 This is particularly useful when conditional logic determines that no further actions are necessary or appropriate.
YAML Snippet 8:
YAML
steps:
  - action:
      action_name: validate_request_data
      output_key: validation_outcome # e.g., { "isValid": false, "reason": "Missing required field 'X'" }
      input_args:
        request_payload: data.incoming_request

  - switch:
      cases:
        - condition: data.validation_outcome.isValid == false
          steps:
            - return: # Early exit if validation fails
                output_mapper:
                  status: "'VALIDATION_FAILED'"
                  error_message: data.validation_outcome.reason
                  details: "'Input data validation failed. Terminating workflow.'"
        # No 'default' needed here if we only care about the failure case for early exit

  # These steps only execute if validation passed and the 'return' was not hit
  - action:
      action_name: process_validated_request
      output_key: processing_result
      input_args:
        payload: data.incoming_request # Original payload, now known to be valid

  - return: # Normal successful exit
      output_mapper:
        status: "'SUCCESS'"
        result: data.processing_result
The return expression is not solely for outputting the final data at the very end of a Compound Action. It also serves as an important control flow mechanism, allowing for clean termination of specific logical paths without requiring complex nested conditions or flags to skip all remaining steps.
5.4. Working with Exposed Data Objects (data, requestor, mw).
Compound Actions operate within an execution context that exposes several key data objects. Understanding these objects is essential for accessing contextual information, user details, action results, and built-in functionalities, enabling dynamic and context-aware operations. The primary exposed data objects are data, requestor, and mw.9
Table 2: Exposed Data Objects in Compound Actions
Object Name	Description	Key Attributes/Access Patterns	Example Usage
data	The central repository for input variables provided to the Compound Action and the results of expressions (actions, scripts) that have reached a COMPLETE state. 9	Accessed via data.input_variable_name or data.output_key_of_a_step. Contains a dictionary where each entry corresponds to an input or an output.	data.user_email_input, data.action1_result.field_name, input_args: { payload: data.previous_step_output }
requestor	Contains information about the user who initiated the Compound Action. Includes all external user proto fields. 9	Accessed via requestor.field_name (e.g., requestor.first_name, requestor.email_addr, requestor.department, requestor.custom_data.some_attribute).	input_args: { requestor_department: requestor.department }, message: $CONCAT(, "")
mw	Provides access to all Moveworks Native (Built-in) Actions that can be used in Compound Actions. 9	Actions are called using mw.action_name as the action_name in an action step.	action_name: mw.create_generic_approval_request, action_name: mw.get_user_by_email
workflow_id	A unique identifier for the specific instance of the Compound Action execution. 9	data.workflow_id (typically) or directly accessible.	Useful for logging or tracking specific workflow runs.
statuses	A mapping from step IDs to their execution status (e.g., PENDING, COMPLETE, ERROR). 9	Potentially for advanced introspection or custom progress tracking logic.	data.statuses.step_id_abc
pending_data	Stores data from expressions currently in the PENDING state. 9	For tracking in-progress operations.	data.pending_data.output_key_of_pending_step
error_data	Contains information about errors encountered during execution, particularly from steps in ERROR or ERROR_HANDLED states. Crucial for catch blocks. 9	Accessed typically as error_data.output_key_of_failed_step within a catch block to get details of the error from the try block.	catch: steps: - script: code: "'Error was: ' + error_data.failed_action_output.message"
progress_updates	A list of messages providing updates on the Compound Action's progress, intended to inform the user. 9	These are often defined within action steps (progress_updates: { on_pending:..., on_complete:... }) rather than read directly.	progress_updates: { on_pending: "Processing your request..." }
The requestor object is particularly valuable as it allows for inherent personalization and context-awareness within Compound Actions. For example, a CA can automatically determine the requester's manager for an approval workflow or tailor responses based on their department, without needing these details to be explicitly passed as input variables every time. This simplifies the design of CAs that need to act on behalf of or in context of the initiating user. The mw object acts as a conveniently namespaced library, providing a clear and standardized way to invoke the powerful capabilities offered by the Moveworks platform's built-in actions.
5.5. Advanced Use of Built-in Actions.
Moveworks offers a suite of sophisticated built-in actions that provide powerful, out-of-the-box capabilities for common and advanced enterprise automation scenarios. These are invoked using the mw.action_name syntax.
Key advanced built-in actions include 11:
•	mw.create_generic_approval_request:
o	Functionality: Creates an in-bot approval request within Moveworks. The associated plugin process can continue if the approval is actioned (approved/denied) within 30 days; otherwise, the request is dropped.
o	Key Input Parameters: 
	approval_key (string, Optional): Specifies a pre-configured approval workflow (e.g., "MANAGER"). If omitted, approvers must be provided.
	approvers (List[User], Optional): A list of User objects from whom approval is sought. Overrides approval_key if provided.
	approval_details (string, Required): The specific details or subject of the approval.
	users_requested_for (List[User], Required): A list of User objects for whom the approval is being requested.
o	Important Note: User-related parameters like approvers and users_requested_for require full User objects, not just email addresses. These objects are typically retrieved using other built-in actions like mw.get_user_by_email or mw.batch_get_users_by_email.
•	mw.generate_structured_value_action:
o	Functionality: Leverages a Large Language Model (LLM) to extract structured data from an input payload based on a defined JSON schema.
o	Key Input Parameters: 
	payload (object, Required): The input data (e.g., text, JSON object) to be analyzed by the LLM.
	output_schema (object, Required): The desired JSON schema for the output (e.g., {"type": "object", "properties": {"name": {"type": "string"}}}}).
	system_prompt (string, Optional): Instructions for the LLM on how to perform the extraction.
	strict (boolean, Optional, default: false): If true, enforces strict adherence to the schema. Requires additionalProperties: false in the schema and all fields to be required.
	model (string, Optional, default: "4o-mini"): Specifies the LLM to use. (Note: GovCloud customers may have different default or supported models, e.g., "gpt-3.5-turbo-0125" if GPT-4o is unsupported 11).
o	Example Use: Extracting key entities from an email body, categorizing a support ticket description based on keywords, or parsing unstructured text into a defined JSON object.
•	mw.generate_text_action:
o	Functionality: Calls an LLM to generate free-form text based on user input and an optional system prompt.
o	Key Input Parameters: 
	user_input (string, Required): The primary input or context for the LLM to generate text from.
	system_prompt (string, Optional): Instructions to guide the LLM's behavior, tone, or response format.
	model (string, Optional, default: "4o-mini"): Specifies the LLM to use.
o	Example Use: Summarizing text, drafting email responses, translating languages, or answering questions based on provided context.
•	User Retrieval Actions (mw.get_user_by_email, mw.batch_get_users_by_email):
o	Functionality: These actions are crucial for retrieving User objects from Moveworks' internal identity store. mw.get_user_by_email fetches a single user record, while mw.batch_get_users_by_email retrieves multiple user records based on a list of email addresses.
o	Importance: As many built-in actions (like mw.create_generic_approval_request or mw.send_plaintext_chat_notification) require User objects or user record IDs as input, these retrieval actions are frequently used prerequisites.
The inclusion of built-in actions like mw.generate_structured_value_action and mw.generate_text_action is particularly noteworthy. It signifies Moveworks embedding powerful LLM capabilities directly within the Compound Action framework. This allows developers to easily incorporate sophisticated AI-driven data processing and text generation into their automated workflows without the complexity of managing external LLM API calls, authentication, and integration themselves. This democratization of AI functionalities within the platform is a key enabler for building more intelligent and capable automations, aligning with the vision of an "Agentic Automation Engine" 5 that can tackle more difficult tasks.
5.6. Advanced Practical Example: Multi-step Approval Workflow with Data Transformation and Conditional Notifications.
This comprehensive example demonstrates how various advanced features of Compound Actions can be combined to create a sophisticated, end-to-end automated process.
Scenario: An employee requests access to a new software application. The workflow should:
1.	Take the software name and justification as input from the user (via a plugin).
2.	Identify the requester's manager.
3.	Allow the requester to optionally specify a different approver if their direct manager is not appropriate (e.g., out of office, or project-specific approval needed).
4.	Transform and validate the request details.
5.	Send an approval request to the designated approver.
6.	Based on the approval status (Approved/Denied), take further actions: 
o	If approved: Call a (mocked) HTTP action to provision the software and notify the user.
o	If denied: Notify the user of the denial with the reason (if provided).
7.	Handle potential errors during the process.
YAML Implementation (Conceptual):
YAML
# Input variables (from plugin/form):
# - software_name (string)
# - justification (string)
# - custom_approver_email (string, optional)

steps:
  # 1. Get Requester's Details (including manager)
  - action:
      action_name: mw.get_user_by_email
      output_key: requestor_details
      input_args:
        user_email: requestor.email_addr # Using the implicit requestor object
      progress_updates:
        on_pending: "Fetching your details..."

  # 2. Determine Approver: Use custom approver if provided, else default to manager
  - script:
      output_key: approver_determination
      input_args:
        custom_email: data.custom_approver_email # Input variable
        manager_email: data.requestor_details.user.manager_email_addr # From previous step
      code: |
        approver_email_to_use = custom_email if custom_email else manager_email
        # Basic validation (more robust validation could be added)
        if not approver_email_to_use:
            # This script will fail if no approver email is found,
            # which should be caught by a try_catch if this is a critical path.
            # For simplicity here, we assume one will be present or raise implicitly.
            raise ValueError("Approver email could not be determined.")
        {"final_approver_email": approver_email_to_use}

  # 3. Get Approver's User Object
  - action:
      action_name: mw.get_user_by_email
      output_key: approver_user_object
      input_args:
        user_email: data.approver_determination.final_approver_email
      progress_updates:
        on_pending: "Fetching approver details..."

  # 4. Prepare Approval Details (Data Transformation)
  - script:
      output_key: formatted_approval_request
      input_args:
        sw_name: data.software_name
        user_justification: data.justification
        requesting_user_name: data.requestor_details.user.full_name
      code: |
        details = "Software Request:\n"
        details += "Application: " + sw_name + "\n"
        details += "Requested by: " + requesting_user_name + "\n"
        details += "Justification: " + user_justification
        details # Return the formatted string

  # 5. Send Approval Request (within a try_catch for robustness)
  - try_catch:
      try:
        steps:
          - action:
              action_name: mw.create_generic_approval_request
              output_key: approval_submission_result
              input_args:
                approvers: [data.approver_user_object.user] # Pass the User object in a list
                approval_details: data.formatted_approval_request
                users_requested_for: [data.requestor_details.user] # User object for whom request is made
              progress_updates:
                on_pending: "Submitting approval request..."
                on_complete: "Approval request submitted. Waiting for response."
      catch:
        steps:
          - action:
              action_name: mw.send_plaintext_chat_notification
              output_key: approval_error_notification
              input_args:
                user_record_id: data.requestor_details.user.id
                message: "There was an error submitting your approval request. Please try again or contact IT support."
          - raise:
              message: "Failed to create approval request. Error: {{error_data.approval_submission_result.error.message}}"
              output_key: approval_creation_failure

  # 6. Process Approval Outcome (Switch on status)
  - switch:
      cases:
        - condition: data.approval_submission_result.status == 'APPROVED'
          steps:
            - action: # Mocked: Call HTTP action to provision software
                action_name: provision_software_api_call # A pre-configured HTTP Action
                output_key: provisioning_status
                input_args:
                  user_id: data.requestor_details.user.id
                  software: data.software_name
                progress_updates:
                  on_pending: "Processing software provisioning..."
                  on_complete: "Software provisioning initiated."
            - action:
                action_name: mw.send_plaintext_chat_notification
                output_key: approval_confirmed_notification
                input_args:
                  user_record_id: data.requestor_details.user.id
                  message: $CONCAT(, "", TRUE)
        - condition: data.approval_submission_result.status == 'DENIED'
          steps:
            - action:
                action_name: mw.send_plaintext_chat_notification
                output_key: denial_notification
                input_args:
                  user_record_id: data.requestor_details.user.id
                  # Ideally, denial reason would be part of approval_submission_result if supported
                  message: $CONCAT(, "", TRUE)
      default: # Handles other statuses like EXPIRED, ERROR from approval action itself
        steps:
          - action:
              action_name: mw.send_plaintext_chat_notification
              output_key: approval_unexpected_status_notification
              input_args:
                user_record_id: data.requestor_details.user.id
                message: $CONCAT(, "", TRUE)
          - raise:
              message: "Unexpected approval status: {{data.approval_submission_result.status}}"
              output_key: unexpected_approval_state

  # 7. Final Return (optional, could summarize outcome)
  - return:
      output_mapper:
        workflow_outcome_status: data.approval_submission_result.status # Or a more processed status
        software_requested: data.software_name
This advanced example illustrates how Compound Actions serve as the "connective tissue" within the Moveworks platform. They orchestrate interactions involving:
•	User context (via the requestor object and inputs).
•	Core platform services (like mw.get_user_by_email for identity, mw.create_generic_approval_request for approvals, and mw.send_plaintext_chat_notification for communication).
•	Custom business logic and data transformation (via script actions).
•	Integrations with external enterprise systems (via HTTP action calls).
•	Sophisticated control flow (switch, try_catch, raise) to manage the process end-to-end.
By combining these elements, Compound Actions become the engine for driving comprehensive, automated processes that can significantly enhance operational efficiency and user experience.
6. Best Practices for Compound Action Development
Developing robust, maintainable, and efficient Compound Actions requires adherence to certain best practices. These guidelines help manage complexity and ensure that automations are reliable and easy to understand.
6.1. Readability, Maintainability, Efficiency.
•	Clear Naming Conventions: Use descriptive and consistent names for output_keys. This makes it easier to understand the data flow and the purpose of each piece of data within the data object. If an action's output is not used by subsequent steps or in the final return, assign its output_key to an underscore (_) to explicitly indicate it's being ignored.9 This improves readability by signaling intent.
•	Modular Design: For very large or complex workflows, consider breaking them down into smaller, more manageable Compound Actions. In post-April 2025 architectures, these smaller CAs might be invokable from a parent plugin or through "Action Activities." While direct CA-to-CA calls might have specific platform support (similar to nesting concepts in other workflow systems 16), the general principle of modularity aids in reusability, testability, and understanding.
•	YAML Commenting: Use comments (#) within the YAML definition to explain complex logic, non-obvious decisions, or the purpose of specific steps or configurations. This is invaluable for future maintenance by yourself or other developers.
•	Prefer Data Mappers/DSL over APIthon for Simple Transformations: For common data transformations like string concatenation, case changes, or simple field mapping, utilize Moveworks Data Mapping Syntax (DSL) directly in input_args or output_mapper where possible.7 This is often more concise, readable, and can be more performant than writing a dedicated APIthon script block for such tasks.
•	Mind APIthon Constraints: Be acutely aware of APIthon's limitations, including size restrictions for code and data structures, and the lack of import capabilities.7 Design APIthon scripts to be focused and efficient. Each Script Action adds a non-negligible latency to plugin execution 7, so their use should be justified.
•	Consistent Formatting: Maintain consistent indentation and formatting in YAML files to improve readability.
The platform's guidance to "use DSL and our Data Mapper for common data transformation operations" instead of immediately resorting to Script Actions is a key best practice.7 Declarative DSL is often easier to read and understand at a glance for data shaping tasks. Furthermore, because Script Actions introduce latency, using built-in mappers for simpler tasks can contribute to better overall performance of the Compound Action.
6.2. Managing Complexity.
As Compound Actions grow in sophistication, managing their complexity becomes paramount.
•	Plan Workflows Before Coding: Before writing any YAML, sketch out the desired workflow logic. Use flowcharts or pseudo-code to define the steps, conditions, data inputs/outputs for each step, and error handling paths. This planning phase can save significant time and prevent overly complicated or flawed designs.
•	Test Incrementally: Build and test Compound Actions in smaller, incremental parts. Verify that individual actions or small sequences behave as expected before integrating them into a larger workflow. Use the testing features within Creator Studio.
•	Utilize progress_updates: For Compound Actions that may take some time to complete, especially those involving multiple external API calls or long-running scripts, use the progress_updates field within action steps.8 Providing on_pending and on_complete messages keeps the user informed about what the bot is doing and manages expectations, improving the user experience. 
YAML
- action:
    action_name: long_running_report_generation
    output_key: report_data
    progress_updates:
      on_pending: "Generating your report, this may take a few moments..."
      on_complete: "Report generation complete!"
•	Structured Error Handling: Implement comprehensive error handling using try_catch blocks for any step that might fail, especially external API calls. Decide on a consistent strategy for handling errors: retry, notify an admin, inform the user, or gracefully terminate.
By following these best practices, developers can create Compound Actions that are not only powerful but also understandable, resilient, and easier to adapt as business requirements evolve.
7. Troubleshooting Common Compound Action Issues
Even with careful design, issues can arise during the development and execution of Compound Actions. Knowing how to debug and interpret common errors is essential.
7.1. Debugging Tips.
•	Creator Studio Logs: The Logs app within Creator Studio is a primary tool for debugging Plugins, Compound Actions, and Actions.5 These logs provide insights into the execution flow, variable states, and any errors encountered. For specific chat interactions, clicking the ℹ️ emoji below a bot response can open a reference page showing the Conversation ID, which can be useful for support or deeper log analysis.5
•	Inspect error_data: When using try_catch blocks, the error_data object (or a similarly named context variable within the catch block) contains details about the error that occurred in the try section. Logging or returning parts of this object can provide specific error messages or codes.
•	HTTP Action Troubleshooting 17: 
o	Validate Externally First: Before implementing an HTTP Action in Moveworks, validate the API call using a tool like Postman. Ensure the request URL, method, headers, authentication, and body are correct and that the API responds as expected. Moveworks often supports cURL import, which can help translate a working Postman request.
o	Check Base URL: Ensure the Base URL in the connector configuration starts with http:// or https://.
o	Variable Escaping: Pay attention to how variables are used in request configurations. Strings typically require triple curly braces ({{{my_string_value}}}) inside quotes (e.g., "{{{my_string_value}}}") to disable HTML escaping, while numbers or booleans might use double braces ({{number}}).
o	Authentication: For multi-step authentication (like OAuth 2.0 Client Credentials that first hit a /token endpoint), ensure the token request is succeeding. "Authorization failed" errors often point to issues here.
•	APIthon Script Debugging 14: 
o	Last Line Return: Remember that only the result of the last executed line in an APIthon script is returned. If intermediate values need to be inspected, ensure they are part of the final returned object (e.g., a dictionary).
o	Type Checking: APIthon is dynamically typed like Python, but ensure data types are as expected, especially when performing operations (e.g., arithmetic on numbers, concatenation on strings). Use conversion functions like str(), int() if necessary.
o	Limits: Keep APIthon's size and operational limits in mind. Overly complex scripts might hit these limits.
o	Simulate Locally (with caution): While APIthon has restrictions, one can sometimes test core Python logic in a standard Python interpreter, keeping in mind the "no imports" and other constraints.
•	Incremental Output: During development, temporarily use return steps with output_mapper to output the contents of the data object or specific intermediate results. This allows inspection of the data flow at various points in the Compound Action.
A common theme in troubleshooting, especially for HTTP Actions, is that many issues stem from mismatches between what Moveworks is configured to send and what the external API expects.17 Thoroughly understanding the target API's requirements and validating requests externally first can prevent many common pitfalls.
7.2. Understanding Error Messages.
Moveworks provides various error messages and codes that can help pinpoint issues.
•	HTTP Action Specific Errors 17:
o	Authorization failed: Often indicates a problem with the authentication setup in the connector, such as incorrect credentials or a failed token request in multi-step auth flows.
o	Failed to construct request: Usually means variables in the request configuration (URL, headers, body) are not being resolved correctly. Ensure all variables denoted by {{{}}} or {{}} have corresponding example values during testing or are correctly populated from the data object during execution.
o	Invalid host name: The base URL in the connector might be malformed (e.g., missing http:// or https://).
o	Unable to resolve host name: The hostname in the base URL may not exist or might be private and inaccessible from the Moveworks environment. Tools like mxtoolbox.com can be used to check DNS records for public hostnames.
o	Failed to make API request: This is often a more generic error that forwards the response from the target API. The details of this error message should contain the actual status code and response body from the external system, which is crucial for diagnosis.
•	General Platform Errors 18:
o	Sorry we cannot process your request this time. (Associated with error codes like 1, 4, 101, 201, 11500, 13000, 13100, 13200, 12911): These are often generic or unknown errors. Contacting Moveworks support is typically recommended if these persist.
o	The item you are trying to delete is still used elsewhere. (Error Code: 13005): This occurs when attempting to delete a component (like a connector) that is still referenced by other use cases or actions. Support may be needed to identify all usages.
o	"Failed to save DSL V1 config: {error_detail}" (Error Code: 13121): Indicates an invalid DSL rule in a configuration field. The {error_detail} part of the message should specify the nature of the DSL error and its location (e.g., "Empty DSL expression for field [...]").
o	"There are errors with the field: {error_detail}": A general configuration error, for instance, using an invalid character when naming a connector. The {error_detail} should provide clues. Connectors, for example, typically only allow lowercase, uppercase, and underscore characters.
Careful examination of the error message, including any specific codes or detailed descriptions, is the first step in troubleshooting. Combined with logs and an understanding of the Compound Action's logic, these messages can guide developers to the root cause of a problem.
8. Conclusions
Moveworks Compound Actions are a cornerstone of the platform's automation capabilities, enabling developers to orchestrate intricate workflows that integrate various services and custom logic. They represent a powerful tool for transforming sequences of technical operations into cohesive, automated business processes.
The evolution from pre-April 2025 to post-April 2025 paradigms marks a significant maturation of the platform. This shift towards a more flexible and granular architecture—where Compound Actions become more "workflow-focused" components within a broader plugin and "Action Activity" structure—empowers developers with greater choice and efficiency. Simpler tasks can be achieved with less overhead, while complex, AI-driven conversational experiences can be more effectively managed at the plugin level, reserving Compound Actions for robust, reusable automation units. Understanding this distinction is paramount for effective development on the current Moveworks platform.
Building effective Compound Actions hinges on a solid grasp of YAML syntax, adept data handling using the data object and output_keys, and skillful application of control flow mechanisms like switch, for, and parallel. The judicious use of APIthon for custom logic and Moveworks Data Mapping Syntax for declarative transformations allows for a balance of flexibility and maintainability. Furthermore, robust error handling with try_catch and raise, coupled with the strategic use of built-in actions (including advanced LLM-powered actions), elevates the sophistication and resilience of the automations.
Adherence to best practices—such as clear naming, modular design, incremental testing, and preferring declarative approaches where feasible—is crucial for developing Compound Actions that are not only powerful but also readable, maintainable, and efficient. As developers embark on creating or enhancing automations, a thorough understanding of these principles, combined with diligent troubleshooting techniques, will pave the way for successful and impactful solutions within the Moveworks ecosystem. Ultimately, Compound Actions serve as a vital engine for realizing the potential of agentic AI, enabling the Moveworks assistant to tackle increasingly complex tasks and deliver significant value to the enterprise.
Appendices
A.1: Comprehensive YAML Syntax Reference
This appendix provides a quick reference for the common YAML constructs used in Moveworks Compound Actions.
Table 3: Compound Action YAML Syntax Reference
Construct	Key / Sub-Key	Type	Mandatory	Description	Example Snippet (Conceptual)
steps	(List of expressions)	list	Yes (for >1 expression)	Defines sequential execution of actions/expressions. 8	steps: [ {action:...}, {script:...} ]
action		dict	Yes (as a step type)	Executes an HTTP Action or a Native (Built-in) Action. 8	- action:
	action_name	string	Yes	Unique identifier of the action to execute (e.g., pre-configured HTTP action name or mw.built_in_action_name).	action_name: my_http_action or action_name: mw.get_user_by_email
	output_key	string	Yes	Variable name to store the action's result in the data object. Use _ if output is not needed.	output_key: user_data_result
	input_args	dict	No	Dictionary of input arguments for the action. Values can be literals or dynamic (data.some_value).	input_args: { user_id: data.id, query: "active" }
	progress_updates	dict	No	Messages for user on pending/completion. Contains on_pending (text) and on_complete (text).	progress_updates: { on_pending: "Working...", on_complete: "Done!" }
	delay_config	dict	No	Delays execution. Keys: milliseconds, seconds, minutes, hours, days. Values are DSL expressions returning numbers. 13	delay_config: { seconds: "5" }
script		dict	Yes (as a step type)	Executes custom APIthon code. 8	- script:
	code	string	Yes	The APIthon code to execute. Use > for multi-line.	code: "value = input_var * 2; value"
	output_key	string	Yes	Variable name to store the script's return value.	output_key: calculated_value
	input_args	dict	No	Dictionary of input arguments accessible within the script.	input_args: { input_var: data.source_value }
switch		dict	Yes (as a step type)	Conditional branching. 8	- switch:
	cases	list[dict]	Yes	List of conditions and their corresponding steps. Each dict has condition (boolean) and steps (list).	cases: [ { condition: data.x > 10, steps: [...] } ]
	default	dict	No	Steps to execute if no cases condition is true. Contains steps (list).	default: { steps: [...] }
for		dict	Yes (as a step type)	Iterates over a collection. 8	- for:
	each	string	Yes	Variable name for the current item in iteration.	each: current_item
	index	string	Yes	Variable name for the index of the current item.	index: item_idx
	in	string	Yes	Name of the iterable variable (e.g., data.my_list).	in: data.list_of_items
	output_key	string	Yes	Variable to store the list of results from each iteration's steps.	output_key: loop_results
	steps	list	No	List of expressions to execute for each item.	steps: [ {script:...} ]
parallel		dict	Yes (as a step type)	Executes expressions concurrently. 8	- parallel:
	for	dict	No (One of for or branches required)	Specifies a loop to execute in parallel (uses each, in, output_key, steps similar to for loop).	for: { in: data.ids, each: id, output_key: par_results, steps: [...] }
	branches	list	No (One of for or branches required)	List of expressions (e.g., actions, scripts) to execute in parallel.	branches: [ {action:...}, {action:...} ]
return		dict	Yes (as a step type)	Exits the Compound Action and returns specified data. 8	- return:
	output_mapper	dict	No	Dictionary defining the structure and values of the returned data, using Moveworks Data Mapping Syntax.	output_mapper: { result_field: data.some_value, message: "'Success'" }
raise		dict	Yes (as a step type)	Stops execution by raising an error. 8	- raise:
	output_key	string	Yes	Variable name where error information is stored.	output_key: error_state
	message	string	No	Error message to be displayed or logged.	message: "Invalid input provided."
try_catch		dict	Yes (as a step type)	Error handling block. 8	- try_catch:
	try	dict	Yes	Contains steps (list) to attempt execution.	try: { steps: [...] }
	catch	dict	Yes	Specifies actions if an error occurs in try. Contains steps (list) and optionally on_status_code.	catch: { on_status_code: ["E404"], steps: [...] }
	on_status_code	int/string/array	No (within catch)	Specific error status code(s) to trigger the catch block. If omitted, catches any error.	on_status_code: [E400, E500] or on_status_code: "CUSTOM_ERROR"
A.2: Moveworks Built-in Actions Catalog
This catalog provides an overview of commonly used Moveworks Built-in Actions (mw.action_name). For a complete and up-to-date list, always refer to the official Moveworks documentation.
Table 4: Moveworks Built-in Actions Overview (Illustrative Selection)
Action Name (mw.action_name)	Functionality	Key Input Parameters (Name, Type, Required, Description)	Example output_key Usage / Typical Output Structure
mw.get_user_by_email	Retrieves a single user record based on email. 11	user_email (string, Yes): Email of the user.	data.user_info.user (User object: id, full_name, email_addr, etc.)
mw.batch_get_users_by_email	Retrieves multiple user records based on a list of emails. 11	user_emails (List[string], Yes): List of email addresses.	data.users_data.user_records (List of User objects)
mw.send_plaintext_chat_notification	Sends a plaintext chat message to a user. 11	user_record_id (string, Yes): Record ID of the target user. <br> message (string, Yes): The message content.	data.notification_status (e.g., success/failure status)
mw.create_generic_approval_request	Creates an in-bot approval request. 11	approval_details (string, Yes): Details requiring approval. <br> users_requested_for (List[User], Yes): Users for whom approval is sought. <br> approval_key (string, No): Predefined approval workflow key. <br> approvers (List[User], No): List of approver User objects (overrides approval_key).	data.approval_result (Object with status like 'APPROVED', 'DENIED', approved_by list, etc.)
mw.generate_structured_value_action	Calls an LLM to produce structured output based on a payload and schema. 11	payload (object, Yes): Data for LLM analysis. <br> output_schema (object, Yes): JSON schema for the desired output. <br> system_prompt (string, No): Instructions for LLM. <br> model (string, No): LLM model to use (e.g., "4o-mini").	data.structured_output.openai_chat_completions_response.choices.message.content (JSON string matching schema)
mw.generate_text_action	Calls an LLM to produce standard text. 11	user_input (string, Yes): Context for text generation. <br> system_prompt (string, No): Model behavior instructions. <br> model (string, No): LLM model to use.	data.generated_text_output.openai_chat_completions_response.choices.message.content (Generated text string)
A.3: APIthon Quick Reference
APIthon is a Python-style scripting language used in Moveworks script actions. It has specific characteristics and limitations.
Table 5: APIthon Quick Reference
Feature	APIthon Detail/Example	Key Limitation/Note
Return Value	Result of the last executed line is returned. 14	my_var = 10; my_var + 5 (returns 15). my_var = 10; my_var +=5 (returns nothing as assignment is last).
Imports	Not allowed. 14	Cannot use import statements. Only built-in functions available.
Class Definitions	Not allowed. 14	Cannot define custom classes. Work with standard data types.
Private Members	Access to items starting with _ is restricted. 14	Avoid using or defining variables/methods like _my_var.
Data Types	Supports numbers (int, float), strings, lists, dictionaries, sets, booleans. 14	Standard Python-like behavior.
String Operations	Concatenation (+), repetition (*), split(), join(), replace(), slicing. 14	text = "hello"; text.upper()
List Operations	append(), pop(), remove(), index(), sort(), reverse(), len(), slicing, comprehensions (limited). 14	my_list = ; my_list.append(3)
Dictionary Operations	Access (``), get(), keys(), values(), assignment. 14	my_dict = {"a":1}; my_dict["b"] = 2
Built-in Functions	len(), str(), int(), float(), bool(), sum(), min(), max(), range(), etc. 14	Check official documentation for the full supported list.
Code Length Limit	e.g., 4096 bytes. 14	Keep scripts concise.
List/String Size Limits	e.g., 2096 bytes for lists, 4096 bytes for strings. 14	Be mindful when processing large data structures.
Number Size Limits	e.g., up to 4294967296. 14	Relevant for very large integer calculations.
Latency	Each Script Action adds non-negligible latency. 7	Prefer DSL/Data Mappers for simple tasks.
A.4: Glossary of Moveworks Terms
•	Action: An individual unit of work within a Compound Action, such as an HTTP call, a script execution, or a built-in Moveworks function. 1
•	Action Activity: (Post-April 2025) A component within a Plugin that can host or invoke Compound Actions or other actions. 1
•	Agentic Automation: An approach where AI agents (like the Moveworks bot) can proactively pursue goals, make decisions, and take actions over extended periods, often leveraging Compound Actions for complex task execution. 3
•	APIthon: A Python-style scripting language used within Moveworks script actions, with specific limitations (no imports, size limits). 8
•	Built-in Action (Native Action): A pre-defined action provided by the Moveworks platform, accessible via the mw. namespace (e.g., mw.get_user_by_email). 11
•	Compound Action: A workflow that orchestrates multiple individual actions (HTTP, APIthon, Built-in) into a single, cohesive automation, using input variables and control flow logic. 1
•	Connector: A configuration that defines the authentication mechanism and base URL for connecting to an external system (e.g., ServiceNow, Salesforce). Used by HTTP Actions. 6
•	Creator Studio: The Moveworks interface used by developers to build, configure, and manage plugins, actions, Compound Actions, and other platform components. 5
•	data Object: A central repository within a Compound Action's execution context that holds input variables and the outputs of completed steps. 9
•	Data Mapping Syntax (Bender/DSL): A Moveworks-specific declarative language used for data manipulation and transformation within YAML, typically in input_args and output_mapper. 7
•	HTTP Action: An action type that makes HTTP/S requests to external APIs and systems. 2
•	Input Variables: Parameters defined for a Compound Action that provide the necessary data for its execution. 1
•	output_key: A YAML key used in action and script steps to name the variable that will store the step's result within the data object. 8
•	Plugin: (In Moveworks context) A user-facing conversational unit or capability deployed to the Moveworks bot, enabling it to understand user requests and perform tasks. Post-April 2025, plugins can directly chain actions or use Action Activities which may contain Compound Actions. 1
•	requestor Object: An exposed data object within a Compound Action containing information about the user who initiated the workflow. 9
•	Script Action: An action type that executes custom code written in APIthon. 2
•	steps Key: The primary YAML key in a Compound Action that introduces a list of actions and expressions to be executed sequentially. 8

