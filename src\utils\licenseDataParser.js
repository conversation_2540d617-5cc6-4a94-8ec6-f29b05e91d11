import yaml from 'js-yaml'

/**
 * Utility functions for parsing Microsoft 365 license data
 */

/**
 * Parses Microsoft 365 license JSON data and extracts relevant information
 * @param {Object|Array} jsonData - The license data from Microsoft Graph API
 * @returns {Array} Array of parsed license objects
 */
export function parseLicenseData(jsonData) {
  let licenseArray = []

  // Handle different JSON structures
  if (Array.isArray(jsonData)) {
    licenseArray = jsonData
  } else if (jsonData.value && Array.isArray(jsonData.value)) {
    licenseArray = jsonData.value
  } else if (jsonData.licenses && Array.isArray(jsonData.licenses)) {
    licenseArray = jsonData.licenses
  } else {
    return []
  }

  return licenseArray.map(license => {
    const displayName = license.displayName || license.skuPartNumber || 'Unknown License'
    const availableLicenses = license.prepaidUnits?.enabled || 0
    const assignedLicenses = license.consumedUnits || 0

    // Determine account type based on license properties
    let accountType = 'Organization'
    if (license.capabilityStatus === 'Enabled' && availableLicenses === 1) {
      accountType = 'Self-service'
    }

    // Calculate usage percentage for progress bar
    const usagePercentage = availableLicenses > 0 ? (assignedLicenses / availableLicenses) * 100 : 0

    return {
      name: displayName,
      availableLicenses,
      assignedLicenses,
      accountType,
      usagePercentage: Math.min(usagePercentage, 100), // Cap at 100%
      skuId: license.skuId,
      skuPartNumber: license.skuPartNumber
    }
  })
}

/**
 * Formats license data for display in a table
 * @param {Array} parsedLicenses - Array of parsed license objects
 * @returns {Object} Formatted data for table display
 */
export function formatLicenseTableData(parsedLicenses) {
  return {
    headers: ['Name', 'Available Licenses', 'Assigned Licenses', 'Account Type'],
    rows: parsedLicenses.map(license => ({
      name: license.name,
      available: license.availableLicenses,
      assigned: license.assignedLicenses,
      accountType: license.accountType,
      usagePercentage: license.usagePercentage,
      skuId: license.skuId
    }))
  }
}

/**
 * Generates YAML representation of license data
 * @param {Array} parsedLicenses - Array of parsed license objects
 * @returns {string} YAML string
 */
export function generateLicenseYaml(parsedLicenses) {
  const yamlData = {
    microsoft_365_licenses: {
      total_licenses: parsedLicenses.length,
      licenses: parsedLicenses.map(license => ({
        name: license.name,
        sku_part_number: license.skuPartNumber,
        available_licenses: license.availableLicenses,
        assigned_licenses: license.assignedLicenses,
        usage_percentage: Math.round(license.usagePercentage * 100) / 100,
        account_type: license.accountType
      }))
    }
  }

  // Convert to YAML with proper formatting
  return yaml.dump(yamlData, {
    indent: 2,
    lineWidth: -1,
    noRefs: true,
    sortKeys: false
  })
}
