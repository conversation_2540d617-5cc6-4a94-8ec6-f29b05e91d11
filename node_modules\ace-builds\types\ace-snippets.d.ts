/* This file is generated using `npm run update-types` */

declare module "ace-builds/src-noconflict/snippets-abc.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-abc" {
    export const snippetText: string;
    export const scope: "abc";
}
declare module "ace-builds/src-noconflict/snippets-actionscript.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-actionscript" {
    export const snippetText: string;
    export const scope: "actionscript";
}
declare module "ace-builds/src-noconflict/snippets-c_cpp.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-c_cpp" {
    export const snippetText: string;
    export const scope: "c_cpp";
}
declare module "ace-builds/src-noconflict/snippets-clojure.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-clojure" {
    export const snippetText: string;
    export const scope: "clojure";
}
declare module "ace-builds/src-noconflict/snippets-coffee.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-coffee" {
    export const snippetText: string;
    export const scope: "coffee";
}
declare module "ace-builds/src-noconflict/snippets-csound_document.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-csound_document" {
    export const snippetText: string;
    export const scope: "csound_document";
}
declare module "ace-builds/src-noconflict/snippets-csound_orchestra.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-csound_orchestra" {
    export const snippetText: string;
    export const scope: "csound_orchestra";
}
declare module "ace-builds/src-noconflict/snippets-css.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-css" {
    export const snippetText: string;
    export const scope: "css";
}
declare module "ace-builds/src-noconflict/snippets-dart.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-dart" {
    export const snippetText: string;
    export const scope: "dart";
}
declare module "ace-builds/src-noconflict/snippets-diff.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-diff" {
    export const snippetText: string;
    export const scope: "diff";
}
declare module "ace-builds/src-noconflict/snippets-django.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-django" {
    export const snippetText: string;
    export const scope: "django";
}
declare module "ace-builds/src-noconflict/snippets-drools.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-drools" {
    export const snippetText: string;
    export const scope: "drools";
}
declare module "ace-builds/src-noconflict/snippets-edifact.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-edifact" {
    export const snippetText: string;
    export const scope: "edifact";
}
declare module "ace-builds/src-noconflict/snippets-erlang.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-erlang" {
    export const snippetText: string;
    export const scope: "erlang";
}
declare module "ace-builds/src-noconflict/snippets-fsl.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-fsl" {
    export const snippetText: string;
    export const scope: "fsl";
}
declare module "ace-builds/src-noconflict/snippets-gobstones.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-gobstones" {
    export const snippetText: string;
    export const scope: "gobstones";
}
declare module "ace-builds/src-noconflict/snippets-graphqlschema.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-graphqlschema" {
    export const snippetText: string;
    export const scope: "graphqlschema";
}
declare module "ace-builds/src-noconflict/snippets-haml.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-haml" {
    export const snippetText: string;
    export const scope: "haml";
}
declare module "ace-builds/src-noconflict/snippets-haskell.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-haskell" {
    export const snippetText: string;
    export const scope: "haskell";
}
declare module "ace-builds/src-noconflict/snippets-html.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-html" {
    export const snippetText: string;
    export const scope: "html";
}
declare module "ace-builds/src-noconflict/snippets-io" {
    export const snippets: ({
        content: string;
        name: string;
        scope: string;
        tabTrigger: string;
        keyEquivalent?: undefined;
    } | {
        content: string;
        keyEquivalent: string;
        name: string;
        scope: string;
        tabTrigger: string;
    } | {
        content: string;
        keyEquivalent: string;
        name: string;
        scope: string;
        tabTrigger?: undefined;
    })[];
    export const scope: "io";
}
declare module "ace-builds/src-noconflict/snippets-java.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-java" {
    export const snippetText: string;
    export const scope: "java";
}
declare module "ace-builds/src-noconflict/snippets-javascript.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-javascript" {
    export const snippetText: string;
    export const scope: "javascript";
}
declare module "ace-builds/src-noconflict/snippets-jsp.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-jsp" {
    export const snippetText: string;
    export const scope: "jsp";
}
declare module "ace-builds/src-noconflict/snippets-liquid.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-liquid" {
    export const snippetText: string;
    export const scope: "liquid";
}
declare module "ace-builds/src-noconflict/snippets-lsl.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-lsl" {
    export const snippetText: string;
    export const scope: "lsl";
}
declare module "ace-builds/src-noconflict/snippets-lua.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-lua" {
    export const snippetText: string;
    export const scope: "lua";
}
declare module "ace-builds/src-noconflict/snippets-makefile.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-makefile" {
    export const snippetText: string;
    export const scope: "makefile";
}
declare module "ace-builds/src-noconflict/snippets-markdown.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-markdown" {
    export const snippetText: string;
    export const scope: "markdown";
}
declare module "ace-builds/src-noconflict/snippets-maze.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-maze" {
    export const snippetText: string;
    export const scope: "maze";
}
declare module "ace-builds/src-noconflict/snippets-perl.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-perl" {
    export const snippetText: string;
    export const scope: "perl";
}
declare module "ace-builds/src-noconflict/snippets-php.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-php" {
    export const snippetText: string;
    export const scope: "php";
}
declare module "ace-builds/src-noconflict/snippets-python.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-python" {
    export const snippetText: string;
    export const scope: "python";
}
declare module "ace-builds/src-noconflict/snippets-r.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-r" {
    export const snippetText: string;
    export const scope: "r";
}
declare module "ace-builds/src-noconflict/snippets-razor.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-razor" {
    export const snippetText: string;
    export const scope: "razor";
}
declare module "ace-builds/src-noconflict/snippets-robot.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-robot" {
    export const snippetText: string;
    export const scope: "robot";
}
declare module "ace-builds/src-noconflict/snippets-rst.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-rst" {
    export const snippetText: string;
    export const scope: "rst";
}
declare module "ace-builds/src-noconflict/snippets-ruby.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-ruby" {
    export const snippetText: string;
    export const scope: "ruby";
}
declare module "ace-builds/src-noconflict/snippets-sh.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-sh" {
    export const snippetText: string;
    export const scope: "sh";
}
declare module "ace-builds/src-noconflict/snippets-snippets.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-snippets" {
    export const snippetText: string;
    export const scope: "snippets";
}
declare module "ace-builds/src-noconflict/snippets-sql.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-sql" {
    export const snippetText: string;
    export const scope: "sql";
}
declare module "ace-builds/src-noconflict/snippets-sqlserver.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-sqlserver" {
    export const snippetText: string;
    export const scope: "sqlserver";
}
declare module "ace-builds/src-noconflict/snippets-tcl.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-tcl" {
    export const snippetText: string;
    export const scope: "tcl";
}
declare module "ace-builds/src-noconflict/snippets-tex.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-tex" {
    export const snippetText: string;
    export const scope: "tex";
}
declare module "ace-builds/src-noconflict/snippets-textile.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-textile" {
    export const snippetText: string;
    export const scope: "textile";
}
declare module "ace-builds/src-noconflict/snippets-vala" {
    export const snippets: {
        content: string;
        name: string;
        scope: string;
        tabTrigger: string;
    }[];
    export const scope: "";
}
declare module "ace-builds/src-noconflict/snippets-velocity.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-velocity" {
    export const snippetText: string;
    export const scope: "velocity";
    export const includeScopes: string[];
}
declare module "ace-builds/src-noconflict/snippets-wollok.snippets" {
    const _exports: string;
    export = _exports;
}
declare module "ace-builds/src-noconflict/snippets-wollok" {
    export const snippetText: string;
    export const scope: "wollok";
}
